<view class="container page-container font-{{globalFontSize}} {{liteMode ? 'lite-mode' : ''}}">
  <!-- Header -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <view class="title">Display</view>
    <view class="placeholder"></view>
  </view>

  <!-- Normal Mode Content -->
  <block wx:if="{{!liteMode}}">
    <!-- Language Section -->
    <view class="setting-section">
      <view class="setting-item" bindtap="toggleLanguagePicker">
        <text class="setting-label">Language</text>
        <view class="setting-value">
          <text class="value-text">{{selectedLanguage}}</text>
          <image src="/images/arrow-down.svg" class="dropdown-arrow {{showLanguagePicker ? 'rotated' : ''}}"></image>
        </view>
      </view>
      
      <!-- Language Picker Dropdown -->
      <view class="language-picker {{showLanguagePicker ? 'show' : ''}}" wx:if="{{showLanguagePicker}}">
        <view class="language-option" wx:for="{{languages}}" wx:key="value" bindtap="selectLanguage" data-language="{{item.label}}">
          <text class="language-text">{{item.label}}</text>
          <image wx:if="{{selectedLanguage === item.label}}" src="/images/check.svg" class="check-icon"></image>
        </view>
      </view>
    </view>

    <!-- Font Size Section -->
    <view class="setting-section">
      <text class="section-title">Font Size</text>
    </view>

    <!-- Preview Section -->
    <view class="preview-section">
      <text class="preview-title">Preview</text>
      <text class="preview-text {{fontSize}}">This text size will be applied to text within the app.</text>
      <text class="preview-date {{fontSize}}">17 Jul, 2025</text>
      
      <!-- Font Size Selector -->
      <view class="font-size-selector">
        <view class="size-option" wx:for="{{fontSizes}}" wx:key="value" bindtap="selectFontSize" data-size="{{item.value}}">
          <text class="size-label {{item.value === 'small' ? 'small' : item.value === 'large' ? 'large' : ''}}">A</text>
          <view class="size-indicator {{fontSize === item.value ? 'active' : ''}}"></view>
          <text class="size-name">{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- Accessibility Design -->
    <view class="setting-section">
      <view class="setting-item" bindtap="navigateToAccessibility">
        <text class="setting-label">Accessibility Design</text>
        <image src="/images/arrow-right.svg" class="arrow-icon"></image>
      </view>
    </view>
  </block>

  <!-- Lite Mode Content -->
  <block wx:if="{{liteMode}}">
    <!-- Language Section Title -->
    <text class="lite-section-title">Language</text>
    
    <!-- Language Options as Buttons -->
    <view class="lite-language-buttons">
      <view class="lite-language-button {{selectedLanguage === '繁體中文' ? 'selected' : ''}}" 
            bindtap="selectLanguage" data-language="繁體中文">
        <text class="lite-language-text">繁體中文</text>
        <image wx:if="{{selectedLanguage === '繁體中文'}}" src="/images/check.svg" class="lite-check-icon"></image>
      </view>
      <view class="lite-language-button {{selectedLanguage === '简体中文' ? 'selected' : ''}}" 
            bindtap="selectLanguage" data-language="简体中文">
        <text class="lite-language-text">简体中文</text>
        <image wx:if="{{selectedLanguage === '简体中文'}}" src="/images/check.svg" class="lite-check-icon"></image>
      </view>
      <view class="lite-language-button {{selectedLanguage === 'English' ? 'selected' : ''}}" 
            bindtap="selectLanguage" data-language="English">
        <text class="lite-language-text">English</text>
        <image wx:if="{{selectedLanguage === 'English'}}" src="/images/check.svg" class="lite-check-icon"></image>
      </view>
    </view>

    <!-- Accessibility Design -->
    <view class="lite-accessibility-item" bindtap="navigateToAccessibility">
      <text class="lite-accessibility-text">Accessibility Design</text>
      <image src="/images/arrow-right.svg" class="lite-arrow-icon"></image>
    </view>
  </block>
</view>





