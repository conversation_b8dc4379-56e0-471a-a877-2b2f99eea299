// pages/notification/read_noti.js
const notificationService = require('../services/notification.js');

Page({
  data: {
    // Notification list
    notifications: [],
    filteredNotifications: [],

    // UI state
    isLoading: false,
    loadingMessage: '',

    // Filter and search
    activeFilter: 'all', // all, unread, read
    searchQuery: '',

    // Empty state
    showEmptyState: false,

    // Pull to refresh
    refreshTriggered: false,

    // Deep link data display
    deepLinkData: '',
    showDeepLinkBox: false,

    // Message display
    message: '',
    messageId: '',
    parsingResult: ''
  },

  onLoad: function () {
    const options = wx.getEnterOptionsSync();

    // Extract messageid from options if it exists
    let extractedMessageId = '';
    let parsingDetails = [];

    try {
      parsingDetails.push('=== MESSAGE ID PARSING RESULTS ===');
      parsingDetails.push('Timestamp: ' + new Date().toISOString());
      parsingDetails.push('');

      // Check if messageid exists directly in options
      if (options.messageid) {
        extractedMessageId = options.messageid;
        parsingDetails.push('✓ Found messageid in options.messageid');
        parsingDetails.push('Value: ' + extractedMessageId);
      }
      // Also check in query parameters if they exist
      else if (options.query && options.query.messageid) {
        extractedMessageId = options.query.messageid;
        parsingDetails.push('✓ Found messageid in options.query.messageid');
        parsingDetails.push('Value: ' + extractedMessageId);
      }
      // Check in extendData if it exists
      else if (options.extendData) {
        const info = this.getUrlParams('?' + options.extendData) || {};
        if (info.messageid) {
          extractedMessageId = info.messageid;
          parsingDetails.push('✓ Found messageid in options.extendData');
          parsingDetails.push('Value: ' + extractedMessageId);
        } else {
          parsingDetails.push('✗ No messageid found in extendData');
          parsingDetails.push('ExtendData keys: ' + Object.keys(info).join(', '));
        }
      } else {
        parsingDetails.push('✗ No messageid found in any location');
        parsingDetails.push('Available options keys: ' + Object.keys(options).join(', '));
        if (options.query) {
          parsingDetails.push('Available query keys: ' + Object.keys(options.query).join(', '));
        }
      }

      parsingDetails.push('');
      parsingDetails.push('Final extracted messageId: ' + (extractedMessageId || '[EMPTY]'));
      parsingDetails.push('MessageId length: ' + extractedMessageId.length);
      parsingDetails.push('MessageId type: ' + typeof extractedMessageId);

    } catch (error) {
      console.error('Error extracting messageid:', error);
      parsingDetails.push('✗ ERROR during parsing: ' + error.message);
    }

    this.setData({
      message: JSON.stringify(options, null, 2),
      messageId: extractedMessageId,
      parsingResult: parsingDetails.join('\n')
    });

    // Log the extracted messageid to console
    console.log('=== MESSAGE ID EXTRACTION ===');
    console.log('Extracted messageId:', extractedMessageId);
    console.log('MessageId length:', extractedMessageId.length);
    console.log('MessageId type:', typeof extractedMessageId);
    if (extractedMessageId) {
      console.log('MessageId found and set successfully');
    } else {
      console.log('No messageId found in options');
      console.log('Available options keys:', Object.keys(options));
    }

    const { extendData } = options;
    if (extendData) {
      const info = this.getUrlParams('?' + extendData) || {}
      const {code, code_verifier} = info
      // Process notification data...
      // this.displayDeepLinkData(info);
    }
  },

  onShow: function() {
    // Refresh notifications when page is shown
    if (this.data.notifications.length > 0) {
      this.loadNotifications();
    }
  },

  onPullDownRefresh: function() {
    console.log('Pull down refresh triggered');
    this.setData({
      refreshTriggered: true
    });
    this.loadNotifications();
  },

  /**
   * Load notifications from service
   */
  loadNotifications: function() {
    const that = this;

    this.setData({
      isLoading: true,
      loadingMessage: 'Loading notifications...'
    });

    // Get user's PushID first
    wx.getPushId({
      success: function(pushRes) {
        if (pushRes.code === 'M00000') {
          const pushId = pushRes.content.pushId;

          // Load notifications using the service
          notificationService.getNotifications(pushId)
            .then(notifications => {
              console.log('Notifications loaded:', notifications);

              that.setData({
                notifications: notifications,
                isLoading: false,
                loadingMessage: '',
                refreshTriggered: false,
                showEmptyState: notifications.length === 0
              });

              that.applyFiltersAndSearch();

              wx.stopPullDownRefresh();
            })
            .catch(error => {
              console.error('Failed to load notifications:', error);

              that.setData({
                isLoading: false,
                loadingMessage: '',
                refreshTriggered: false,
                showEmptyState: true
              });

              wx.stopPullDownRefresh();

              const userMessage = notificationService.getErrorMessage(error.code);
              wx.showToast({
                title: userMessage,
                icon: 'error',
                duration: 3000
              });
            });
        } else {
          that.handleLoadError(pushRes.code, pushRes.message);
        }
      },
      fail: function(err) {
        console.error('Failed to get PushID:', err);
        that.handleLoadError('PUSH_ID_ERROR', 'Failed to retrieve PushID');
      }
    });
  },

  /**
   * Handle loading errors
   */
  handleLoadError: function(errorCode, errorMessage) {
    this.setData({
      isLoading: false,
      loadingMessage: '',
      refreshTriggered: false,
      showEmptyState: true
    });

    wx.stopPullDownRefresh();

    const userMessage = notificationService.getErrorMessage(errorCode);
    wx.showToast({
      title: userMessage,
      icon: 'error',
      duration: 3000
    });
  },

  /**
   * Apply filters and search to notifications
   */
  applyFiltersAndSearch: function() {
    const { notifications, activeFilter, searchQuery } = this.data;
    let filtered = [...notifications];

    // Apply status filter
    if (activeFilter === 'unread') {
      filtered = filtered.filter(noti => !noti.isRead);
    } else if (activeFilter === 'read') {
      filtered = filtered.filter(noti => noti.isRead);
    }

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(noti =>
        noti.pushMessagesEn.toLowerCase().includes(query) ||
        noti.pushMessagesTc.toLowerCase().includes(query) ||
        noti.pushMessagesSc.toLowerCase().includes(query) ||
        (noti.messageDetailEn && noti.messageDetailEn.toLowerCase().includes(query)) ||
        (noti.messageDetailTc && noti.messageDetailTc.toLowerCase().includes(query)) ||
        (noti.messageDetailSc && noti.messageDetailSc.toLowerCase().includes(query))
      );
    }

    this.setData({
      filteredNotifications: filtered
    });
  },

  /**
   * Handle filter button taps
   */
  onFilterTap: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter
    });
    this.applyFiltersAndSearch();
  },

  /**
   * Handle search input
   */
  onSearchInput: function(e) {
    const query = e.detail.value;
    this.setData({
      searchQuery: query
    });
    this.applyFiltersAndSearch();
  },

  /**
   * Clear search
   */
  clearSearch: function() {
    this.setData({
      searchQuery: ''
    });
    this.applyFiltersAndSearch();
  },

  /**
   * Mark notification as read/unread
   */
  toggleReadStatus: function(e) {
    const index = e.currentTarget.dataset.index;
    const notification = this.data.filteredNotifications[index];

    if (!notification) return;

    const that = this;
    const newStatus = !notification.isRead;

    // Optimistically update UI
    const updatedNotifications = [...this.data.filteredNotifications];
    updatedNotifications[index].isRead = newStatus;
    this.setData({
      filteredNotifications: updatedNotifications
    });

    // Update in service
    notificationService.updateNotificationStatus(notification.id, newStatus)
      .then(() => {
        console.log('Notification status updated successfully');
        // Reload to ensure consistency
        setTimeout(() => {
          that.loadNotifications();
        }, 500);
      })
      .catch(error => {
        console.error('Failed to update notification status:', error);

        // Revert optimistic update
        const revertedNotifications = [...that.data.filteredNotifications];
        revertedNotifications[index].isRead = !newStatus;
        that.setData({
          filteredNotifications: revertedNotifications
        });

        const userMessage = notificationService.getErrorMessage(error.code);
        wx.showToast({
          title: userMessage,
          icon: 'error',
          duration: 2000
        });
      });
  },

  /**
   * Delete notification
   */
  deleteNotification: function(e) {
    const index = e.currentTarget.dataset.index;
    const notification = this.data.filteredNotifications[index];

    if (!notification) return;

    const that = this;

    wx.showModal({
      title: 'Delete Notification',
      content: 'Are you sure you want to delete this notification?',
      confirmText: 'Delete',
      confirmColor: '#ff4444',
      success: function(res) {
        if (res.confirm) {
          // Optimistically remove from UI
          const updatedNotifications = [...that.data.filteredNotifications];
          updatedNotifications.splice(index, 1);
          that.setData({
            filteredNotifications: updatedNotifications
          });

          // Delete from service
          notificationService.deleteNotification(notification.id)
            .then(() => {
              console.log('Notification deleted successfully');
              // Reload to ensure consistency
              that.loadNotifications();
            })
            .catch(error => {
              console.error('Failed to delete notification:', error);

              // Revert optimistic update
              that.loadNotifications();

              const userMessage = notificationService.getErrorMessage(error.code);
              wx.showToast({
                title: userMessage,
                icon: 'error',
                duration: 2000
              });
            });
        }
      }
    });
  },

  /**
   * Navigate back to previous page
   */
  navigateBack: function() {
    wx.navigateBack();
  },

  /**
   * Format notification time
   */
  formatTime: function(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;

    return date.toLocaleDateString();
  },

  /**
   * Get notification status icon
   */
  getStatusIcon: function(isRead) {
    return isRead ? 'check' : 'notification';
  },

  /**
   * Toggle search bar visibility
   */
  toggleSearch: function() {
    // For now, search is always visible, but this can be extended
    console.log('Search toggle tapped');
  },

  /**
   * View notification details (can be extended for full screen view)
   */
  viewNotificationDetails: function(e) {
    const index = e.currentTarget.dataset.index;
    const notification = this.data.filteredNotifications[index];

    if (!notification) return;

    console.log('View notification details:', notification);

    // For now, just toggle read status when tapped
    // This can be extended to show a detailed view
    this.toggleReadStatus(e);
  },

  /**
   * Check if notification has detailed message
   */
  hasDetailMessage: function(notification) {
    return notification.messageDetailEn || notification.messageDetailTc || notification.messageDetailSc;
  },

  /**
   * Get detail message preview
   */
  getDetailPreview: function(notification, language) {
    if (language === 'en' && notification.messageDetailEn) {
      return notification.messageDetailEn.substring(0, 100) + (notification.messageDetailEn.length > 100 ? '...' : '');
    } else if (language === 'tc' && notification.messageDetailTc) {
      return notification.messageDetailTc.substring(0, 100) + (notification.messageDetailTc.length > 100 ? '...' : '');
    } else if (language === 'sc' && notification.messageDetailSc) {
      return notification.messageDetailSc.substring(0, 100) + (notification.messageDetailSc.length > 100 ? '...' : '');
    }
    return '';
  },

  /**
   * Parse URL parameters from query string
   */
  getUrlParams: function(url) {
    try {
      const params = {};
      const queryString = url.split('?')[1];
      if (queryString) {
        const pairs = queryString.split('&');
        for (const pair of pairs) {
          const [key, value] = pair.split('=');
          if (key) {
            params[decodeURIComponent(key)] = decodeURIComponent(value || '');
          }
        }
      }
      return params;
    } catch (error) {
      console.error('Failed to parse URL params:', error);
      return {};
    }
  },

  /**
   * Display deep link data in textbox
   */
  displayDeepLinkData: function(info) {
    if (Object.keys(info).length === 0) {
      this.setData({
        deepLinkData: '',
        showDeepLinkBox: false
      });
      return;
    }

    // Format the data as JSON for display
    const formattedData = JSON.stringify(info, null, 2);

    this.setData({
      deepLinkData: formattedData,
      showDeepLinkBox: true
    });

    console.log('Deep link data displayed:', info);
  },

  /**
   * Hide deep link data display
   */
  hideDeepLinkData: function() {
    this.setData({
      deepLinkData: '',
      showDeepLinkBox: false
    });
  }
});