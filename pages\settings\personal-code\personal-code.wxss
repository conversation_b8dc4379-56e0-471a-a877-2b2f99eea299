.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-top: 20rpx; /* Use minimal top padding */
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20rpx;
}

.back-button, .placeholder {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.content {
  background-color: white;
  padding: 40rpx;
  border-radius: 10rpx;
  text-align: center;
}

.placeholder-text {
  color: #666;
  font-size: 28rpx;
}





