.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  height: auto;
  overflow: visible;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.services-list {
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10rpx;
  padding: 20rpx;
  /* Ensure all items are visible without scrolling */
  height: auto;
  max-height: none;
  overflow: visible;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
  margin: 5rpx;
  min-height: 140rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.service-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
}

.service-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.3;
  word-wrap: break-word;
  max-width: 100%;
}

.service-item {
  position: relative; /* For favorite button positioning */
}

.favorite-button {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Font size classes */
.font-small .header-title {
  font-size: 32rpx;
}

.font-small .service-title {
  font-size: 20rpx;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .service-title {
  font-size: 28rpx;
}

/* Responsive grid for different screen sizes */
@media (max-width: 750rpx) {
  .services-list {
    grid-template-columns: 1fr 1fr;
    gap: 8rpx;
    padding: 15rpx;
    /* Ensure full visibility on mobile */
    height: auto;
    max-height: none;
    overflow: visible;
  }

  .service-item {
    min-height: 120rpx;
    padding: 15rpx;
  }

  .service-icon {
    width: 50rpx;
    height: 50rpx;
  }

  .service-title {
    font-size: 22rpx;
  }
}

/* Ensure all services are always visible */
.services-list {
  /* Remove any potential height limitations */
  contain: none;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* Notification icon specific styling */
.service-icon[src*="notification.svg"] {
  filter: hue-rotate(90deg) saturate(1.5); /* Make it more green to match theme */
}


