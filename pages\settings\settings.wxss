.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 140rpx; /* Add padding to account for the tab bar */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Profile Header */
.profile-header {
  background: linear-gradient(to right, #009966, #ff9933);
  padding: 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  min-height: 100rpx;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .profile-header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
}

.profile-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #009966;
  margin-right: 20rpx;
}

.profile-name {
  flex: 1;
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  align-items: center;
}

/* Font size classes for profile header */
.font-small .profile-name {
  font-size: 36rpx;
}

.font-default .profile-name {
  font-size: 40rpx;
}

.font-large .profile-name {
  font-size: 44rpx;
}

.eye-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
  filter: brightness(0) invert(1);
}

.profile-edit {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  padding: 8rpx;
}

.logout-icon,
.login-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* Settings Section */
.settings-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.item-icon {
  margin-right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-version {
  color: #666666;
  font-size: 28rpx;
}

.version-text {
  color: #666666;
}

.setting-icon {
  width: 48rpx;
  height: 48rpx;
}

.item-title {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
}

.toggle-item {
  border-bottom: 1px solid #f0f0f0;
}

.lite-switch {
  transform: scale(0.8);
}

/* Settings List */
.settings-list {
  display: flex;
  flex-direction: column;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.item-arrow {
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* Tab Bar */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #ffffff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 22rpx;
  color: #999999;
}

.tab-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tab-subtext {
  font-size: 18rpx;
  color: #999999;
  margin-top: -4rpx;
}

.active-text {
  color: #006633;
  font-weight: 500;
}

.scan-tab {
  position: relative;
}

.scan-circle {
  width: 100rpx;
  height: 100rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: absolute;
  top: -30rpx;
}

.scan-icon {
  width: 48rpx;
  height: 48rpx;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20%;
  height: 6rpx;
  background-color: #006633;
  border-radius: 3rpx 3rpx 0 0;
}

/* Font size classes */
.font-small .section-title,
.font-small .item-title,
.font-small .profile-name {
  font-size: 28rpx;
}

.font-large .section-title,
.font-large .item-title {
  font-size: 36rpx;
}

.font-large .profile-name {
  font-size: 48rpx;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
  border-bottom: none !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 50rpx;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.bottom-nav .nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.bottom-nav .nav-item.active {
  color: #006633;
}

.bottom-nav .nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -10rpx;
  margin-bottom: -6rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}

/* Font size classes for homepage */
.font-small .nav-text {
  font-size: 20rpx;
}

.font-default .nav-text {
  font-size: 22rpx;
}

.font-large .nav-text {
  font-size: 24rpx;
}

/* Font size classes for navigation - exact copy from index */
.font-small .nav-text {
  font-size: 20rpx;
}

.font-default .nav-text {
  font-size: 28rpx;
}

.font-large .nav-text {
  font-size: 32rpx;
}

/* Font size classes for navigation */
.font-small .nav-text,
.font-default .nav-text,
.font-large .nav-text,
.bottom-nav .font-small .nav-text,
.font-small .bottom-nav .nav-text,
.bottom-nav .font-default .nav-text,
.font-default .bottom-nav .nav-text,
.bottom-nav .font-large .nav-text,
.font-large .bottom-nav .nav-text {
  font-size: 28rpx !important;
}

/* Lite mode specific override */
.lite-mode .nav-text {
  font-size: 32rpx;
}

.lite-mode .bottom-nav .nav-text {
  font-size: 32rpx;
}

.font-small .login,
.font-small .temperature,
.font-small .nav-text,
.font-small .news-headline,
.font-small .tab,
.font-small .more,
.font-small .service-title,
.font-small .harbour-title,
.font-small .harbour-location,
.font-small .harbour-destination {
  font-size: 26rpx;
}

.font-default .login,
.font-default .temperature,
.font-default .nav-text,
.font-default .news-headline,
.font-default .tab,
.font-default .more,
.font-default .service-title,
.font-default .harbour-title,
.font-default .harbour-location,
.font-default .harbour-destination {
  font-size: 28rpx;
}

.font-large .login,
.font-large .temperature,
.font-large .nav-text,
.font-large .news-headline,
.font-large .tab,
.font-large .more,
.font-large .service-title,
.font-large .harbour-title,
.font-large .harbour-location,
.font-large .harbour-destination {
  font-size: 32rpx;
}

/* Force navigation text size with maximum specificity */
.container.font-large .bottom-nav .nav-text,
.container.lite-mode .bottom-nav .nav-text {
  font-size: 32rpx !important;
}

.page-container.font-large .bottom-nav .nav-text,
.page-container.lite-mode .bottom-nav .nav-text {
  font-size: 32rpx !important;
}

.font-large .bottom-nav .nav-item .nav-text {
  font-size: 32rpx !important;
}

.lite-mode .bottom-nav .nav-item .nav-text {
  font-size: 32rpx !important;
}

.bottom-nav .nav-text {
  font-size: 28rpx;
  line-height: 1;
}

/* Font size classes for navigation - with higher specificity */
.font-small .bottom-nav .nav-text {
  font-size: 26rpx;
}

.font-default .bottom-nav .nav-text {
  font-size: 28rpx;
}

.font-large .bottom-nav .nav-text {
  font-size: 32rpx;
}

.lite-mode .bottom-nav .nav-text {
  font-size: 32rpx;
}

