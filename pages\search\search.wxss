.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
  border-bottom: none !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.search-box {
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.search-input-container {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  margin: 0 20rpx;
  font-size: 32rpx;
}

.hot-services-section {
  margin-top: 0;
  background-color: #e8f5f0;
}

.section-title {
  padding: 20rpx;
  font-size: 32rpx;
  color: #006633;
  font-weight: 500;
}

.service-list {
  background-color: white;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #e0e0e0;
}

.favorite-button {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.service-content {
  flex: 1;
}

.service-title {
  font-size: 32rpx;
  color: #333;
}

.arrow-icon {
  margin-left: 20rpx;
}

.keyboard-suggestions {
  position: fixed;
  bottom: 308rpx;
  left: 0;
  right: 0;
  display: flex;
  background-color: #d1d5db;
  border-top: 1px solid #c0c0c0;
  height: 80rpx;
}

.suggestion-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  border-right: 1px solid #c0c0c0;
}

.suggestion-item:last-child {
  border-right: none;
}

/* Font size classes */
.font-small .section-title {
  font-size: 28rpx;
}

.font-small .service-title {
  font-size: 28rpx;
}

.font-small .search-input {
  font-size: 28rpx;
}

.font-default .section-title {
  font-size: 32rpx;
}

.font-default .service-title {
  font-size: 32rpx;
}

.font-default .search-input {
  font-size: 32rpx;
}

.font-large .section-title {
  font-size: 36rpx;
}

.font-large .service-title {
  font-size: 36rpx;
}

.font-large .search-input {
  font-size: 36rpx;
}

/* Font size classes for navigation */
.font-small .nav-text {
  font-size: 20rpx;
}

.font-default .nav-text {
  font-size: 22rpx;
}

.font-large .nav-text {
  font-size: 24rpx;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 28rpx !important;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -10rpx;
  margin-bottom: -6rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}




