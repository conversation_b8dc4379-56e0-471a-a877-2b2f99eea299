<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Notification</text>
  </view>

  <!-- Scrollable Content -->
  <scroll-view class="content" scroll-y="true">

    <!-- Section 1: Retrieve My PushID -->
    <view class="section">
      <text class="section-title">Retrieve My PushID</text>

      <view class="pushid-section">
        <button class="primary-button" bindtap="getPushId">
          What is my PushID?
        </button>

        <view class="pushid-display {{showPushId ? 'visible' : 'hidden'}}">
          <input class="pushid-input selectable" value="{{myPushId}}" disabled="true" placeholder="Your PushID will appear here (long press to copy)"></input>
          <text class="copy-hint">(setClipboardData not implemented!))</text>
        </view>
        <view class="copy-button-container {{showPushId ? 'visible' : 'hidden'}}">
          <button class="copy-button" bindtap="copyPushId" disabled="{{!myPushId}}">
            Copy
          </button>
        </view>
      </view>
    </view>

    <!-- Section 2: Write Notification -->
    <view class="section">
      <text class="section-title">Write Notification</text>

      <view class="notification-form">
        <!-- Target PushID Input (Required) -->
        <view class="form-group">
          <text class="form-label">PushID: <text class="required">*</text></text>
          <input class="form-input" value="{{targetPushId}}" bindinput="onPushIdInput" placeholder="Enter recipient's PushID (32 characters) - Required"></input>
        </view>

        <!-- Multilingual Message Inputs (Optional) -->
        <view class="form-group">
          <text class="form-label optional">English:</text>
          <textarea class="form-textarea" value="{{pushMessagesEn}}" bindinput="onEnInput" placeholder="Notification message (English) - Optional" maxlength="128"></textarea>
          <text class="char-counter">{{pushMessagesEn.length}}/128</text>
        </view>

        <view class="form-group">
          <text class="form-label optional">繁體中文:</text>
          <textarea class="form-textarea" value="{{pushMessagesTc}}" bindinput="onTcInput" placeholder="Notification message (繁體中文) - Optional" maxlength="128"></textarea>
          <text class="char-counter">{{pushMessagesTc.length}}/128</text>
        </view>

        <view class="form-group">
          <text class="form-label optional">简体中文:</text>
          <textarea class="form-textarea" value="{{pushMessagesSc}}" bindinput="onScInput" placeholder="Notification message (简体中文) - Optional" maxlength="128"></textarea>
          <text class="char-counter">{{pushMessagesSc.length}}/128</text>
        </view>

        <!-- Optional Detailed Messages -->
        <view class="form-group">
          <text class="form-label optional">Detailed Message (Optional):</text>
          <text class="form-hint">💡 Optional: Leave empty if banner message is sufficient</text>

          <view class="language-tabs">
            <button class="tab-button {{activeDetailTab === 'tc' ? 'active' : ''}}" bindtap="switchDetailTab" data-lang="tc">繁中</button>
            <button class="tab-button {{activeDetailTab === 'sc' ? 'active' : ''}}" bindtap="switchDetailTab" data-lang="sc">简中</button>
            <button class="tab-button {{activeDetailTab === 'en' ? 'active' : ''}}" bindtap="switchDetailTab" data-lang="en">EN</button>
          </view>

          <textarea class="form-textarea" value="{{currentDetailMessage}}" bindinput="onDetailInput" placeholder="Detailed message (max 1024 chars)" maxlength="1024"></textarea>
          <text class="char-counter">{{currentDetailMessage.length}}/1024</text>
        </view>

        <!-- Network Test and Send Buttons -->
        <view class="button-row">
          <button class="secondary-button" bindtap="testNetworkConnection">
            Test Network
          </button>
          <button class="send-button {{canSend ? 'enabled' : 'disabled'}}" bindtap="sendNotification" disabled="{{!canSend}}">
            Send
          </button>
        </view>
      </view>

      <!-- Log Section - Displays errors or success responses -->
      <view class="error-log-section {{showLog ? 'visible' : 'hidden'}}">
        <view class="error-log-container">
          <view class="error-log-header">
            <text class="error-log-title">{{logType === 'success' ? 'Response Log' : 'Error Log'}} ({{logCount}} entries)</text>
            <view class="error-log-controls">
              <button class="error-log-button" bindtap="clearLog">
                Clear
              </button>
              <button class="error-log-button" bindtap="toggleLog">
                Hide
              </button>
            </view>
          </view>
          <textarea class="error-log-text" value="{{logContent}}" disabled="true" placeholder="{{logType === 'success' ? 'Response will appear here...' : 'Errors will appear here...'}}"></textarea>
        </view>
      </view>

      <!-- Task ID Section - Display after successful send -->
      <view class="taskid-section {{taskId ? 'visible' : 'hidden'}}">
        <text class="section-title">Task ID</text>
        <textarea class="form-textarea" value="{{taskId}}" disabled="true" placeholder="Task ID:"></textarea>
      </view>
    </view>

  </scroll-view>

  <!-- Loading Overlay -->
  <view class="loading-overlay {{isLoading ? 'visible' : 'hidden'}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingMessage}}</text>
  </view>

</view>