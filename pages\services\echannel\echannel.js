Page({
  data: {
    // Page data
  },
  
  onLoad: function() {
    // Initialize the page
  },
  
  navigateBack: function() {
    wx.navigateBack();
  },
  
  showHelp: function() {
    wx.showModal({
      title: 'eChannel Help',
      content: 'For assistance with eChannel services, please call the Immigration Department hotline at 2824 6111 or visit the nearest Immigration Office.',
      showCancel: false,
      confirmText: 'OK'
    });
  },
  
  navigateToService: function(e) {
    const service = e.currentTarget.dataset.service;
    
    switch(service) {
      case 'enrollment':
        wx.navigateTo({
          url: '/pages/services/echannel/enrollment/enrollment'
        });
        break;
      case 'status':
        wx.navigateTo({
          url: '/pages/services/echannel/status/status'
        });
        break;
      case 'renewal':
        wx.navigateTo({
          url: '/pages/services/echannel/renewal/renewal'
        });
        break;
      case 'locations':
        wx.navigateTo({
          url: '/pages/services/echannel/locations/locations'
        });
        break;
      default:
        wx.showToast({
          title: 'Service coming soon',
          icon: 'none'
        });
    }
  }
})