.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.back-button, .info-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .info-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

/* Code Type Selector */
.code-selector {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #e0e0e0;
}

.selector-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.selector-item.active {
  color: #006633;
  font-weight: 500;
}

.selector-item.active:after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #006633;
  border-radius: 2rpx;
}

/* QR Code Display */
.code-display {
  background-color: #ffffff;
  margin: 0 30rpx 30rpx;
  border-radius: 10rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code {
  width: 400rpx;
  height: 400rpx;
  margin-bottom: 40rpx;
}

.health-code {
  width: 400rpx;
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.health-code.green {
  background-color: #4CAF50;
}

.health-code.yellow {
  background-color: #FFC107;
}

.health-code.red {
  background-color: #F44336;
}

.health-status {
  font-size: 60rpx;
  font-weight: bold;
  color: #ffffff;
  text-transform: uppercase;
}

.code-info {
  width: 100%;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
}

.info-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.status-active {
  color: #4CAF50;
}

.status-green {
  color: #4CAF50;
}

.status-yellow {
  color: #FFC107;
}

.status-red {
  color: #F44336;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-around;
  margin: 0 30rpx 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.button-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.action-button text {
  font-size: 24rpx;
  color: #666666;
}

/* Instructions */
.instructions {
  margin: 0 30rpx 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.instructions-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.instructions-content {
  display: flex;
  flex-direction: column;
}

.instructions-content text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

/* Verification Notice */
.verification-notice {
  margin: 0 30rpx;
  background-color: #e8f5f0;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
}

.notice-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.verification-notice text {
  font-size: 24rpx;
  color: #006633;
  line-height: 1.5;
}

/* Font size classes */
.font-small .title {
  font-size: 32rpx;
}

.font-small .selector-item {
  font-size: 24rpx;
}

.font-small .info-label,
.font-small .info-value {
  font-size: 24rpx;
}

.font-small .instructions-title {
  font-size: 26rpx;
}

.font-small .instructions-content text {
  font-size: 24rpx;
}

.font-small .verification-notice text {
  font-size: 20rpx;
}

.font-large .title {
  font-size: 40rpx;
}

.font-large .selector-item {
  font-size: 32rpx;
}

.font-large .info-label,
.font-large .info-value {
  font-size: 32rpx;
}

.font-large .instructions-title {
  font-size: 34rpx;
}

.font-large .instructions-content text {
  font-size: 32rpx;
}

.font-large .verification-notice text {
  font-size: 28rpx;
}
