<view class="container page-container font-{{globalFontSize}} {{liteMode ? 'lite-mode' : ''}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <!-- Normal Mode Content -->
  <block wx:if="{{!liteMode}}">
    <!-- Banner Section -->
    <view class="banner">
      <view class="banner-left">
        <view class="title" wx:if="{{userInfo.isLoggedIn}}">
          {{greeting}}! {{userInfo.englishName || userInfo.name}}{{userInfo.chineseName ? ' ' + userInfo.chineseName : ''}}
        </view>
        <view class="title" wx:else>{{greeting}}!</view>
        <view class="company-name" wx:if="{{userInfo.isLoggedIn && userInfo.company}}">{{userInfo.company}}</view>
        <text class="login" bindtap="onLoginLogoutTap">{{userInfo.isLoggedIn ? 'Logout' : 'Login'}} ></text>
      </view>
      <view class="banner-right">
        <view class="weather-box">
          <image src="{{weatherIcon}}" class="weather-icon"></image>
          <text class="temperature">{{temperature}}</text>
        </view>
        <view class="alert-icons">
          <image wx:for="{{alertIcons}}" wx:key="index" src="{{item.src}}" class="alert-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- Navigation Bar -->
    <view class="nav-bar">
      <view class="nav-item" wx:for="{{navItems}}" wx:key="index" bindtap="onNavItemTap" data-index="{{index}}">
        <image src="/images/{{item.icon}}.svg" class="nav-icon"></image>
        <text class="nav-text">{{item.text}}</text>
      </view>
    </view>
    
    <!-- News Section -->
    <view class="news-ticker">
      <view class="news-ticker-content" style="transform: translateX(-{{currentNewsIndex * 100}}%)">
        <view class="news-headline" wx:for="{{newsHeadlines}}" wx:key="index">
          {{item}}
        </view>
      </view>
    </view>
    
    <!-- Tab Section -->
    <view class="tab-section">
      <view class="tab {{activeTab === 'featured' ? 'active' : ''}}" bindtap="switchTab" data-tab="featured">Featured Services</view>
      <view class="tab {{activeTab === 'topics' ? 'active' : ''}}" bindtap="switchTab" data-tab="topics">Topics</view>
      <view class="more" bindtap="navigateToMore">More</view>
    </view>
    
    <!-- Services Grid -->
    <view class="services-grid" wx:if="{{activeTab === 'featured'}}">
      <view class="service-item" wx:for="{{featuredServices}}" wx:key="index" bindtap="onServiceTap" data-index="{{index}}">
        <image src="{{item.currentIcon || item.icon}}" class="service-icon"></image>
        <text class="service-title">{{item.currentTitle || item.title}}</text>
      </view>
    </view>
    
    <!-- Topics Grid -->
    <view class="topics-grid" wx:if="{{activeTab === 'topics'}}">
      <view class="topic-item" wx:for="{{topicServices}}" wx:key="index">
        <image src="{{item.currentIcon || item.icon}}" class="topic-icon"></image>
        <text class="topic-title">{{item.currentTitle || item.title}}</text>
      </view>
    </view>
    
    <!-- Harbour Info Section -->
    <view class="harbour-section">
      <view class="section-header">
        <text class="section-title">Harbour Info</text>
        <text class="update-time">Updated: {{updateTime}}</text>
      </view>
      
      <view class="harbour-info">
        <view class="harbour-card" wx:for="{{harbourInfo}}" wx:key="index">
          <view class="harbour-title">{{item.name}}</view>
          <view class="harbour-location">{{item.location}}</view>
          <view class="harbour-speeds">
            <view class="speed-item" wx:for="{{item.speeds}}" wx:for-item="speed" wx:key="index">
              <text class="speed-value">{{speed.value}}</text>
              <text class="speed-unit">{{speed.unit}}</text>
            </view>
          </view>
          <view class="harbour-destination">{{item.destination}}</view>
          <view class="harbour-fees" wx:if="{{item.fees}}">
            <text class="fee-item">🚗{{item.fees.car}}</text>
            <text class="fee-item">🚚{{item.fees.truck}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- Lite Mode Content -->
  <block wx:if="{{liteMode}}">
    <!-- Banner Section (same as normal) -->
    <view class="banner">
      <view class="banner-left">
        <text class="title" wx:if="{{userInfo.isLoggedIn}}">
          {{greeting}}! {{userInfo.englishName || userInfo.name}}{{userInfo.chineseName ? ' ' + userInfo.chineseName : ''}}
        </text>
        <text class="title" wx:else>{{greeting}}!</text>
        <text class="company-name" wx:if="{{userInfo.isLoggedIn && userInfo.company}}">{{userInfo.company}}</text>
        <text class="login" bindtap="onLoginLogoutTap">{{userInfo.isLoggedIn ? 'Logout' : 'Login'}} ></text>
      </view>
      <view class="banner-right">
        <view class="weather-box">
          <image src="{{weatherIcon}}" class="weather-icon"></image>
          <text class="temperature">{{temperature}}</text>
        </view>
        <view class="alert-icons">
          <image wx:for="{{alertIcons}}" wx:key="index" src="{{item.src}}" class="alert-icon"></image>
        </view>
      </view>
    </view>

    <!-- Main Action Buttons Grid -->
    <view class="lite-main-actions">
      <view class="lite-action-button" bindtap="onNavItemTap" data-index="0">
        <image src="/images/search.svg" class="lite-main-icon"></image>
        <text class="lite-main-text">Search</text>
      </view>
      <view class="lite-action-button" bindtap="onNavItemTap" data-index="1">
        <image src="/images/info.svg" class="lite-main-icon"></image>
        <text class="lite-main-text">Info</text>
      </view>
      <view class="lite-action-button" bindtap="onNavItemTap" data-index="2">
        <image src="/images/inbox.svg" class="lite-main-icon"></image>
        <text class="lite-main-text">Inbox</text>
      </view>
      <view class="lite-action-button" bindtap="onScanTap">
        <image src="/images/scan.svg" class="lite-main-icon"></image>
        <text class="lite-main-text">Scan</text>
      </view>
    </view>

    <!-- Featured Services Section -->
    <view class="lite-featured-section">
      <view class="lite-featured-header">
        <text class="lite-featured-title">Featured Services</text>
        <text class="lite-more-link" bindtap="navigateToMore">More</text>
      </view>
      <view class="lite-featured-grid">
        <view class="lite-featured-item" wx:for="{{featuredServices}}" wx:key="index">
          <text class="lite-featured-text">{{item.currentTitle || item.title}}</text>
        </view>
      </view>
    </view>
  </block>

  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item active" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>





















