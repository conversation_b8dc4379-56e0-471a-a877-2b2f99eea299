Page({
  data: {
    selectedLanguage: 'English',
    showLanguagePicker: false,
    languages: [
      { value: 'zh-hk', label: '繁體中文' },
      { value: 'zh-cn', label: '简体中文' },
      { value: 'en', label: 'English' }
    ],
    fontSize: 'default',
    fontSizes: [
      { value: 'small', label: 'Small' },
      { value: 'default', label: 'Default' },
      { value: 'large', label: 'Large' }
    ],
    globalFontSize: 'default',
    liteMode: false
  },

  onLoad: function() {
    // Load saved settings
    const savedLanguage = wx.getStorageSync('language') || 'English';
    const savedFontSize = wx.getStorageSync('fontSize') || 'default';
    
    // Load global font size and lite mode
    const app = getApp();
    const liteMode = app.globalData.liteMode;
    
    this.setData({
      selectedLanguage: savedLanguage,
      fontSize: savedFontSize,
      globalFontSize: savedFontSize,
      liteMode: typeof liteMode === 'boolean' ? liteMode : false
    });
  },

  onShow: function() {
    // Update font size and lite mode when returning to page
    const app = getApp();
    const liteMode = app.globalData.liteMode;
    const fontSize = liteMode ? 'large' : app.globalData.fontSize;
    
    this.setData({
      globalFontSize: fontSize,
      liteMode: typeof liteMode === 'boolean' ? liteMode : false
    });
  },

  toggleLanguagePicker: function() {
    this.setData({
      showLanguagePicker: !this.data.showLanguagePicker
    });
  },

  selectLanguage: function(e) {
    const language = e.currentTarget.dataset.language;
    this.setData({
      selectedLanguage: language,
      showLanguagePicker: false
    });
    
    // Save to storage
    wx.setStorageSync('language', language);
  },

  selectFontSize: function(e) {
    const fontSize = e.currentTarget.dataset.size;
    this.setData({
      fontSize: fontSize,
      globalFontSize: fontSize
    });
    
    // Apply globally through app instance
    const app = getApp();
    app.setGlobalFontSize(fontSize);
  },

  navigateToAccessibility: function() {
    wx.navigateTo({
      url: '/pages/settings/accessibility/accessibility'
    });
  },

  navigateBack: function() {
    wx.navigateBack();
  }
})


