<view class="container">
  <view class="welcome-section">
    <text class="welcome-text">Welcome {{userName}}{{chineseName ? ' ' + chineseName : ''}}</text>
  </view>
  
  <view class="company-section">
    <text class="company-label">Please select your company</text>
    
    <view class="company-list">
      <view class="company-item" wx:for="{{companies}}" wx:key="index" wx:if="{{index > 0}}" bindtap="selectCompany" data-index="{{index}}">
        <view class="radio-button {{companyIndex === index ? 'selected' : ''}}">
          <view class="radio-inner" wx:if="{{companyIndex === index}}"></view>
        </view>
        <text class="company-name">{{item}}</text>
      </view>
    </view>
    
    <view class="continue-button {{companyIndex > 0 ? 'enabled' : 'disabled'}}" bindtap="continueLogin">
      Continue
    </view>
  </view>
</view>
