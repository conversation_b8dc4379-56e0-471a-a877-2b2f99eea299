Component({
  properties: {
    active: {
      type: String,
      value: 'home'
    }
  },
  
  methods: {
    navigateTo: function(e) {
      const page = e.currentTarget.dataset.page;
      
      if (page === this.properties.active) {
        // Already on this page, do nothing
        return;
      }
      
      if (page === 'home') {
        wx.redirectTo({
          url: '/pages/index/index'
        });
      } else if (page === 'services') {
        wx.redirectTo({
          url: '/pages/services/services'
        });
      } else if (page === 'scan') {
        // Temporarily disabled due to scanner crashes
        wx.showToast({
          title: 'Scan feature temporarily unavailable',
          icon: 'none'
        });
        
        /*
        // Handle scan functionality - invoke camera
        wx.scanCode({
          success: (res) => {
            console.log('Scan result:', res);
            // Handle the scan result
            wx.showToast({
              title: 'Scanned: ' + res.result,
              icon: 'success'
            });
          },
          fail: (err) => {
            console.log('Scan failed:', err);
            wx.showToast({
              title: 'Scan cancelled',
              icon: 'none'
            });
          }
        });
        */
      } else if (page === 'me') {
        wx.redirectTo({
          url: '/pages/me/me'
        });
      } else if (page === 'settings') {
        wx.redirectTo({
          url: '/pages/settings/settings'
        });
      }
    }
  }
})






