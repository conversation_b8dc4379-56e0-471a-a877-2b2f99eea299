.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-top: 20rpx; /* Use minimal top padding */
}

.setting-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.setting-label {
  font-size: 30rpx;
  color: #333333;
}

.setting-value {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
}

.value-text {
  font-size: 30rpx;
  color: #006633;
  margin-right: 10rpx;
}

.dropdown-arrow {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.language-picker {
  position: absolute;
  right: 20rpx;
  width: 300rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.language-picker.show {
  max-height: 300rpx;
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.language-text {
  font-size: 28rpx;
  color: #333333;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
}

.preview-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.preview-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.preview-text.small {
  font-size: 24rpx;
}

.preview-text.large {
  font-size: 32rpx;
}

.preview-date {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
  display: block;
}

.preview-date.small {
  font-size: 24rpx;
}

.preview-date.large {
  font-size: 32rpx;
}

.font-size-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30rpx;
}

.size-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.size-label {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.size-label.small {
  font-size: 24rpx;
}

.size-label.large {
  font-size: 40rpx;
}

.size-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin-bottom: 10rpx;
}

.size-indicator.active {
  background-color: #006633;
}

.size-name {
  font-size: 24rpx;
  color: #666666;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20rpx;
  /* Remove custom margin, let container padding handle positioning */
}

.back-button, .placeholder {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

/* Lite Mode Styles - Override normal mode completely */
.lite-mode .header {
  margin-bottom: 0;
}

.lite-mode .setting-section,
.lite-mode .preview-section {
  display: none !important;
}

.lite-section-title {
  font-size: 36rpx;
  color: #333333;
  margin: 40rpx 40rpx 30rpx 40rpx;
  font-weight: normal;
  display: block;
}

.lite-language-buttons {
  margin: 0 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.lite-language-button {
  background-color: #ffffff;
  border: 2rpx solid #d0d0d0;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80rpx;
}

.lite-language-button.selected {
  background-color: #2d7d6b;
  border-color: #2d7d6b;
}

.lite-language-text {
  font-size: 34rpx;
  color: #333333;
  font-weight: normal;
}

.lite-language-button.selected .lite-language-text {
  color: #ffffff;
}

.lite-check-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

.lite-accessibility-item {
  background-color: transparent;
  padding: 40rpx;
  margin: 80rpx 0 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e0e0e0;
}

.lite-accessibility-text {
  font-size: 36rpx;
  color: #333333;
  font-weight: normal;
}

.lite-arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}











