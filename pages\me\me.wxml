<view class="container page-container font-{{globalFontSize}} {{liteMode ? 'lite-mode' : ''}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Personal Assistant (Testing)</text>
    <view class="refresh-button">
      <icon type="refresh" size="24"></icon>
    </view>
  </view>
  
  <!-- Tab Navigation -->
  <view class="tab-navigation">
    <view class="tab {{activeTab === 'profiles' ? 'active' : ''}}" bindtap="switchTab" data-tab="profiles">
      <text>Profiles</text>
    </view>
    <view class="tab {{activeTab === 'todo' ? 'active' : ''}}" bindtap="switchTab" data-tab="todo">
      <text>To-do</text>
    </view>
    <view class="tab {{activeTab === 'status' ? 'active' : ''}}" bindtap="switchTab" data-tab="status">
      <text>Status</text>
    </view>
  </view>

  <!-- Normal Mode Content (unchanged) -->
  <block wx:if="{{!liteMode}}">
    <!-- Disclaimer Banner -->
    <view class="disclaimer-banner" wx:if="{{activeTab === 'todo' || activeTab === 'status'}}">
      <text>Users must agree to allow "iAM Smart" to obtain the personal information including Bills, Balances, To-do Lists and Application Status, through a system managed by the Digital Policy Office (DPO).</text>
    </view>

    <!-- Tab Content Container with Swipe -->
    <view class="tab-content-container" 
          bindtouchstart="handleTouchStart" 
          bindtouchmove="handleTouchMove" 
          bindtouchend="handleTouchEnd"
          wx:if="{{activeTab === 'todo' || activeTab === 'status'}}">
      
      <!-- To-do Content -->
      <view class="tab-content" wx:if="{{activeTab === 'todo'}}">
        <!-- Carousel/Slider -->
        <view class="carousel">
          <view class="carousel-container" style="transform: translateX(-{{currentSlideIndex * 100}}%)">
            <view class="carousel-slide" wx:for="{{slides}}" wx:key="id">
              <image src="{{item.image}}" mode="aspectFit" class="slide-image"></image>
            </view>
          </view>
          
          <!-- Navigation Arrows -->
          <view class="carousel-nav-left" bindtap="prevSlide">
            <text>‹</text>
          </view>
          <view class="carousel-nav-right" bindtap="nextSlide">
            <text>›</text>
          </view>
          
          <!-- Dots Indicator -->
          <view class="carousel-dots">
            <view wx:for="{{slides}}" wx:key="id" 
                  class="carousel-dot {{currentSlideIndex === index ? 'active' : ''}}"
                  bindtap="goToSlide" data-index="{{index}}">
            </view>
          </view>
        </view>
        
        <!-- Add Button -->
        <view class="add-button" bindtap="addAssistant">
          <text>Add</text>
        </view>
      </view>
      
      <!-- Status Content -->
      <view class="tab-content" wx:if="{{activeTab === 'status'}}">
        <!-- Carousel/Slider (same as in To-do tab) -->
        <view class="carousel">
          <view class="carousel-container" style="transform: translateX(-{{currentSlideIndex * 100}}%)">
            <view class="carousel-slide" wx:for="{{slides}}" wx:key="id">
              <image src="{{item.image}}" mode="aspectFit" class="slide-image"></image>
            </view>
          </view>
          
          <!-- Navigation Arrows -->
          <view class="carousel-nav-left" bindtap="prevSlide">
            <text>‹</text>
          </view>
          <view class="carousel-nav-right" bindtap="nextSlide">
            <text>›</text>
          </view>
          
          <!-- Dots Indicator -->
          <view class="carousel-dots">
            <view wx:for="{{slides}}" wx:key="id" 
                  class="carousel-dot {{currentSlideIndex === index ? 'active' : ''}}"
                  bindtap="goToSlide" data-index="{{index}}">
            </view>
          </view>
        </view>
        
        <!-- Add Button -->
        <view class="add-button" bindtap="addAssistant">
          <text>Add</text>
        </view>
      </view>
    </view>

    <!-- Profiles Content (outside swipe container) -->
    <view class="tab-content profiles-content" wx:if="{{activeTab === 'profiles'}}">
      <!-- Top row with two cards -->
      <view class="profile-row">
        <view class="profile-card" 
              style="background-color: {{profileItems[0].background}}">
          <view class="profile-card-content">
            <view class="profile-info">
              <image src="{{profileItems[0].icon}}" class="profile-icon"></image>
              <view class="profile-text-container">
                <text class="profile-title">{{profileItems[0].title}}</text>
                <text class="profile-description" wx:if="{{profileItems[0].description}}">{{profileItems[0].description}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="profile-card" 
              style="background-color: {{profileItems[1].background}}">
          <view class="profile-card-content">
            <view class="profile-info">
              <image src="{{profileItems[1].icon}}" class="profile-icon"></image>
              <view class="profile-text-container">
                <text class="profile-title">{{profileItems[1].title}}</text>
                <text class="profile-description" wx:if="{{profileItems[1].description}}">{{profileItems[1].description}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- Digital Document card -->
      <view class="profile-card full-width" 
            style="background-color: {{profileItems[2].background}}">
        <view class="profile-card-content">
          <view class="profile-info">
            <view class="profile-text-container">
              <text class="profile-title white">{{profileItems[2].title}}</text>
              <text class="profile-description white">{{profileItems[2].description}}</text>
            </view>
          </view>
          <view class="add-circle" wx:if="{{profileItems[2].hasAddButton}}">
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>
      
      <!-- e-ME Profile card -->
      <view class="profile-card full-width" 
            style="background-color: {{profileItems[3].background}}">
        <view class="profile-card-content">
          <view class="profile-info">
            <view class="profile-text-container">
              <text class="profile-title green">{{profileItems[3].title}}</text>
              <text class="profile-description green">{{profileItems[3].description}}</text>
            </view>
          </view>
          <view class="arrow-circle" wx:if="{{profileItems[3].hasArrow}}">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- Lite Mode Content -->
  <block wx:if="{{liteMode}}">
    <!-- To-do Tab Content (Lite Mode) -->
    <view wx:if="{{activeTab === 'todo'}}">
      <!-- Disclaimer Box -->
      <view class="lite-disclaimer-box">
        <text class="lite-disclaimer-text">Users must agree to allow "iAM Smart" to obtain the personal information including Bills, Balances, To-do Lists and Application Status, through a system managed by the Digital Policy Office (DPO).</text>
      </view>
      
      <!-- Phone Image -->
      <view class="lite-phone-container">
        <image src="/images/assistant-phone.png" mode="aspectFit" class="lite-phone-image"></image>
      </view>
      
      <!-- Add Button -->
      <view class="lite-add-button" bindtap="addAssistant">
        <text>Add</text>
      </view>
    </view>
    
    <!-- Status Tab Content (Lite Mode) - Same as To-do -->
    <view wx:if="{{activeTab === 'status'}}">
      <!-- Disclaimer Box -->
      <view class="lite-disclaimer-box">
        <text class="lite-disclaimer-text">Users must agree to allow "iAM Smart" to obtain the personal information including Bills, Balances, To-do Lists and Application Status, through a system managed by the Digital Policy Office (DPO).</text>
      </view>
      
      <!-- Phone Image -->
      <view class="lite-phone-container">
        <image src="/images/assistant-phone.png" mode="aspectFit" class="lite-phone-image"></image>
      </view>
      
      <!-- Add Button -->
      <view class="lite-add-button" bindtap="addAssistant">
        <text>Add</text>
      </view>
    </view>
    
    <!-- Other tabs in lite mode (placeholder for now) -->
    <view wx:if="{{activeTab === 'profiles'}}">
      <text class="empty-text">Lite mode profiles coming soon</text>
    </view>
  </block>
  
  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item active" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>

















