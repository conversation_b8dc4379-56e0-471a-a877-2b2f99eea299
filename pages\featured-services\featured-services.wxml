<view class="container font-{{globalFontSize}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Featured Services</text>
  </view>
  
  <view class="services-list">
    <view class="service-item" wx:for="{{services}}" wx:key="index" bindtap="onServiceTap" data-index="{{index}}">
      <view class="favorite-button" bindtap="toggleFavorite" data-index="{{index}}">
        <icon type="{{item.favorite ? 'success' : 'circle'}}" size="20" color="{{item.favorite ? '#006633' : '#cccccc'}}"></icon>
      </view>
      <view class="service-content">
        <image wx:if="{{item.icon}}" src="{{item.icon}}" class="service-icon" mode="aspectFit"></image>
        <text class="service-title">{{item.title}}</text>
      </view>
    </view>
  </view>
  
  <!-- Common Tab Bar Component -->
  <tab-bar active="services"></tab-bar>
</view>




