<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <view class="title">Digital Assistant</view>
    <view class="settings-button">
      <image src="/images/settings.svg" class="settings-icon"></image>
    </view>
  </view>
  
  <!-- Introduction Slides (shown only on first visit) -->
  <swiper class="intro-slides" indicator-dots="true" autoplay="true" interval="3000" duration="500" current="{{currentSlide}}">
    <block wx:for="{{slides}}" wx:key="id">
      <swiper-item>
        <view class="slide-content">
          <image src="{{item.image}}" class="slide-image" mode="aspectFit"></image>
          <view class="slide-title">{{item.title}}</view>
          <view class="slide-description">{{item.description}}</view>
        </view>
      </swiper-item>
    </block>
  </swiper>
  
  <!-- Chat Container -->
  <view class="chat-container" id="chat-container">
    <block wx:for="{{messages}}" wx:key="index">
      <view class="message {{item.type === 'user' ? 'user-message' : 'assistant-message'}}">
        <view class="message-content">
          <text>{{item.content}}</text>
        </view>
        <view class="message-time">{{item.time}}</view>
      </view>
    </block>
  </view>
  
  <!-- Suggestions -->
  <view class="suggestions-container" wx:if="{{showSuggestions && messages.length <= 1}}">
    <view class="suggestions-title">You might want to ask:</view>
    <view class="suggestions-list">
      <block wx:for="{{suggestions}}" wx:key="index">
        <view class="suggestion-item" bindtap="useSuggestion" data-suggestion="{{item}}">
          {{item}}
        </view>
      </block>
    </view>
  </view>
  
  <!-- Input Area -->
  <view class="input-container">
    <view class="input-box">
      <input type="text" placeholder="Type your question..." value="{{inputValue}}" bindinput="handleInput" confirm-type="send" bindconfirm="sendMessage"></input>
      <view class="voice-button" bindtap="toggleVoiceInput">
        <image src="/images/voice.svg" class="voice-icon"></image>
      </view>
    </view>
    <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage">
      <text>Send</text>
    </view>
  </view>
</view>