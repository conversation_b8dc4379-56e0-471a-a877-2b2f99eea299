<view class="container font-{{globalFontSize}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <view class="search-input-container">
      <input class="search-input" placeholder="Search services" value="{{searchQuery}}" bindinput="onSearchInput" focus="{{showKeyboard}}" />
    </view>
  </view>
  
  <view class="hot-services">
    <view class="section-title">Hot Services</view>
    <view class="service-list">
      <view class="service-item" wx:for="{{hotServices}}" wx:key="index">
        <text class="service-title">{{item.title}}</text>
        <icon type="{{item.favorite ? 'success' : 'circle'}}" size="20" color="{{item.favorite ? '#006633' : '#cccccc'}}"></icon>
      </view>
    </view>
  </view>
  
  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>





