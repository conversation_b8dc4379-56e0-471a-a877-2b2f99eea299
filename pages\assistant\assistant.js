Page({
  data: {
    slides: [
      {
        id: 1,
        image: '/images/assistant-slide1.png',
        title: 'Welcome to Your Digital Assistant',
        description: 'Your personal guide to government services and information'
      },
      {
        id: 2,
        image: '/images/assistant-slide2.png',
        title: 'Ask Questions Naturally',
        description: 'Get answers about services, applications, and procedures'
      },
      {
        id: 3,
        image: '/images/assistant-slide3.png',
        title: 'Complete Tasks Efficiently',
        description: 'Book appointments, check status, and get reminders'
      }
    ],
    currentSlide: 0,
    messages: [],
    inputValue: '',
    showSuggestions: true,
    suggestions: [
      'How do I renew my ID card?',
      'What documents do I need for a passport?',
      'Where is the nearest government office?',
      'When is the tax filing deadline?'
    ]
  },

  onLoad: function() {
    // Start with a welcome message
    this.setData({
      messages: [
        {
          type: 'assistant',
          content: 'Hello! I\'m your digital assistant. How can I help you today?',
          time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
        }
      ]
    });
  },

  changeSlide: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentSlide: index
    });
  },

  handleInput: function(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  sendMessage: function() {
    if (!this.data.inputValue.trim()) return;
    
    const userMessage = {
      type: 'user',
      content: this.data.inputValue,
      time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    };
    
    // Add user message to chat
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      showSuggestions: false
    });
    
    // Simulate assistant response after a short delay
    setTimeout(() => {
      const assistantMessage = {
        type: 'assistant',
        content: this.getAssistantResponse(userMessage.content),
        time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };
      
      this.setData({
        messages: [...this.data.messages, assistantMessage]
      });
      
      // Scroll to the bottom
      this.scrollToBottom();
    }, 1000);
  },
  
  getAssistantResponse: function(userInput) {
    // Simple response logic - in a real app, this would connect to a backend service
    const input = userInput.toLowerCase();
    
    if (input.includes('id card') || input.includes('identity card')) {
      return 'To renew your ID card, you need to make an appointment at the Immigration Department. You can do this through the eChannel service or by visiting a Registration of Persons Office.';
    } else if (input.includes('passport')) {
      return 'For a passport application, you\'ll need your ID card, a completed application form, and the application fee. You can apply online through the Immigration Department website or in person.';
    } else if (input.includes('government office') || input.includes('nearest')) {
      return 'I can help you find the nearest government office. Could you share your current location or provide a district name?';
    } else if (input.includes('tax') || input.includes('filing')) {
      return 'The tax filing deadline is usually by the end of April each year. For the exact date and more information, please check the Inland Revenue Department website.';
    } else {
      return 'I understand you\'re asking about "' + userInput + '". Let me find some information for you on this topic.';
    }
  },
  
  useSuggestion: function(e) {
    const suggestion = e.currentTarget.dataset.suggestion;
    this.setData({
      inputValue: suggestion
    }, () => {
      this.sendMessage();
    });
  },
  
  toggleVoiceInput: function() {
    // To Do: Implement voice recognition
    wx.showToast({
      title: 'Voice input activated',
      icon: 'none'
    });
  },
  
  scrollToBottom: function() {
    wx.createSelectorQuery()
      .select('#chat-container')
      .boundingClientRect(function(rect){
        wx.pageScrollTo({
          scrollTop: rect.height,
          duration: 300
        });
      })
      .exec();
  },
  
  navigateBack: function() {
    wx.navigateBack();
  }
})