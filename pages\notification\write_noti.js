const notificationService = require('../services/notification.js');

Page({
  data: {
    // PushID retrieval
    myPushId: '',
    showPushId: false,

    // Notification form - Start with empty PushID for testing validation
    targetPushId: '', // Start empty - user will enter valid PushID to enable Send
    pushMessagesTc: '測試通知訊息',
    pushMessagesSc: '测试通知消息',
    pushMessagesEn: 'Test notification message',
    messageDetailTc: '這是測試詳細訊息',
    messageDetailSc: '这是测试详细消息',
    messageDetailEn: 'This is a test detailed message',
    activeDetailTab: 'tc',
    canSend: false, // Start disabled - will enable when valid PushID is entered

    // UI state
    isLoading: false,
    loadingMessage: '',

    // General logging (for errors and success responses)
    logContent: '',
    logType: 'none', // 'none', 'error', 'success'
    showLog: false,
    logCount: 0,

    // Task ID from successful notification send
    taskId: ''
  },

  onLoad: function(options) {
    console.log('Notification page loaded');

    // Validate form with default values to enable Send button
    setTimeout(() => {
      this.validateForm();
    }, 100);
  },

  onShow: function() {
    // Reset form state when page is shown
    this.resetFormState();
  },

  /**
   * Reset form to initial state
   */
  resetFormState: function() {
   this.setData({
     targetPushId: '',
     pushMessagesTc: '',
     pushMessagesSc: '',
     pushMessagesEn: '',
     messageDetailTc: '',
     messageDetailSc: '',
     messageDetailEn: '',
     canSend: false,
     isLoading: false,
     loadingMessage: '',
     taskId: ''
     // Note: logContent, logType, showLog, logCount are NOT reset here to preserve the response/error for user review
   });
 },

  /**
   * Get user's PushID using iAM Smart API
   */
  getPushId: function() {
    const that = this;

    console.log('Retrieving PushID...');
    this.setData({
      isLoading: true,
      loadingMessage: 'Retrieving your PushID...'
    });

    wx.getPushId({
      success: function(res) {
        console.log('PushID retrieval success:', res);

        if (res.code === 'M00000') {
          const pushId = res.content.pushId;
          console.log('Retrieved PushID:', pushId);

          that.setData({
            myPushId: pushId,
            showPushId: true,
            isLoading: false,
            loadingMessage: ''
          });

          wx.showToast({
            title: 'PushID retrieved',
            icon: 'success',
            duration: 2000
          });
        } else {
          that.setData({
            isLoading: false,
            loadingMessage: ''
          });

          // Handle iAM Smart specific error codes
          that.handlePushIdError(res.code, res.message);
        }
      },
      fail: function(err) {
        console.error('PushID retrieval failed:', err);

        that.setData({
          isLoading: false,
          loadingMessage: ''
        });

        // Log error to textbox
        that.logErrorToTextbox('getPushId', err, {
          step: 'wx.getPushId fail',
          errorType: 'API_ERROR'
        });

        wx.showToast({
          title: 'Failed to retrieve PushID',
          icon: 'error',
          duration: 3000
        });
      }
    });
  },

  /**
   * Handle PushID retrieval errors with user-friendly messages
   * @param {string} errorCode - iAM Smart error code
   * @param {string} errorMessage - Raw error message
   */
  handlePushIdError: function(errorCode, errorMessage) {
    const userMessage = notificationService.getErrorMessage(errorCode);

    wx.showModal({
      title: 'PushID Error',
      content: userMessage + '\n\nError Code: ' + errorCode,
      showCancel: false,
      confirmText: 'OK'
    });

    // Log error for debugging
    notificationService.logError('getPushId', { code: errorCode, message: errorMessage });
  },

  /**
   * Copy PushID to clipboard (Selectable text approach)
   */
  copyPushId: function() {
    const that = this;
    const pushId = this.data.myPushId;

    console.log('Selectable PushID copy initiated:', {
      hasPushId: !!pushId,
      pushIdLength: pushId ? pushId.length : 0,
      pushIdPrefix: pushId ? pushId.substring(0, 8) + '...' : 'N/A'
    });

    if (!pushId) {
      const errorMsg = 'No PushID available to copy';
      console.error(errorMsg);

      // Log error to textbox
      that.logErrorToTextbox('copyPushId', new Error(errorMsg), {
        pushId: '[EMPTY]',
        operation: 'validation',
        reason: 'PushID is null or undefined'
      });

      wx.showToast({
        title: 'No PushID to copy',
        icon: 'error'
      });
      return;
    }

    if (pushId.length !== 32) {
      const errorMsg = `Invalid PushID length: expected 32, got ${pushId.length}`;
      console.error(errorMsg);

      // Log error to textbox
      that.logErrorToTextbox('copyPushId', new Error(errorMsg), {
        pushId: pushId.substring(0, 8) + '...',
        expectedLength: 32,
        actualLength: pushId.length,
        operation: 'validation',
        reason: 'PushID length validation failed'
      });

      wx.showToast({
        title: 'Invalid PushID format',
        icon: 'error'
      });
      return;
    }

    // Show success message since the text is now selectable
    wx.showToast({
      title: 'PushID ready for copying',
      icon: 'success',
      duration: 3000
    });

    console.log('PushID made selectable for copying');
  },

  /**
   * Handle target PushID input changes
   */
  onPushIdInput: function(e) {
    const value = e.detail.value;
    this.setData({
      targetPushId: value
    });
    this.validateForm();
  },

  /**
   * Handle Traditional Chinese message input
   */
  onTcInput: function(e) {
    const value = e.detail.value;
    this.setData({
      pushMessagesTc: value
    });
    this.validateForm();
  },

  /**
   * Handle Simplified Chinese message input
   */
  onScInput: function(e) {
    const value = e.detail.value;
    this.setData({
      pushMessagesSc: value
    });
    this.validateForm();
  },

  /**
   * Handle English message input
   */
  onEnInput: function(e) {
    const value = e.detail.value;
    this.setData({
      pushMessagesEn: value
    });
    this.validateForm();
  },

  /**
   * Handle detailed message input (for current active tab)
   */
  onDetailInput: function(e) {
    const value = e.detail.value;
    const activeTab = this.data.activeDetailTab;

    // Update the appropriate detail message based on active tab
    const updateData = {};
    updateData[`messageDetail${activeTab.toUpperCase()}`] = value;

    this.setData(updateData);
  },

  /**
   * Switch between detail message language tabs
   */
  switchDetailTab: function(e) {
    const lang = e.currentTarget.dataset.lang;
    this.setData({
      activeDetailTab: lang,
      currentDetailMessage: this.data[`messageDetail${lang.toUpperCase()}`] || ''
    });
  },

  /**
   * Validate form and update send button state
   * Only PushID is required - message fields are optional
   */
  validateForm: function() {
    const { targetPushId } = this.data;

    console.log('Validating form:', {
      targetPushId: targetPushId,
      targetPushIdLength: targetPushId ? targetPushId.length : 0
    });

    // Only PushID is required for sending notifications
    const isPushIdValid = notificationService.validatePushId(targetPushId);

    console.log('Validation results:', {
      isPushIdValid: isPushIdValid,
      pushIdLength: targetPushId ? targetPushId.length : 0
    });

    const canSend = isPushIdValid; // Only require valid PushID

    console.log('Final canSend state:', canSend);

    this.setData({
      canSend: canSend
    });
  },

  /**
    * Send notification via iAM Smart API
    */
   sendNotification: function() {
     const that = this;
     const { targetPushId, pushMessagesTc, pushMessagesSc, pushMessagesEn, messageDetailTc, messageDetailSc, messageDetailEn } = this.data;

     console.log('Sending notification to:', targetPushId);
     this.setData({
       isLoading: true,
       loadingMessage: 'Sending notification...'
     });

     // Prepare notification data - only PushID is required
     const notificationData = {
       pushIds: [targetPushId],
       pushMessagesTc: pushMessagesTc || '',
       pushMessagesSc: pushMessagesSc || '',
       pushMessagesEn: pushMessagesEn || ''
     };

     // Add detailed messages if provided (optional)
     if (messageDetailTc || messageDetailSc || messageDetailEn) {
       notificationData.messageDetailTc = messageDetailTc || '';
       notificationData.messageDetailSc = messageDetailSc || '';
       notificationData.messageDetailEn = messageDetailEn || '';
     }

    // Send notification via service
    notificationService.sendNotification(notificationData)
       .then(response => {
         console.log('Notification sent successfully:', response);

         // Extract and store taskId if available
         console.log('Response object:', response);
         console.log('Response.taskId:', response.taskId);
         console.log('Response has taskId property:', response.hasOwnProperty('taskId'));

         const taskId = response && response.taskId ? response.taskId : '';
         console.log('Extracted taskId:', taskId);

         // Format the response for display as JSON with safe replacer
         let formattedResponse;
         try {
           formattedResponse = JSON.stringify(response, (key, value) => {
             // Replace undefined with null to avoid issues
             if (value === undefined) return null;
             return value;
           }, 2);

           // Debug: Log the full response length
           console.log('Full response length:', formattedResponse.length);
           console.log('Full response content:', formattedResponse);

         } catch (e) {
           console.warn('Failed to stringify response:', e);
           formattedResponse = response.toString ? response.toString() : 'Unable to display response';
         }

         // Debug: Check if response is being truncated
         if (formattedResponse.length > 1000) {
           console.warn('Response is quite long, checking for potential truncation issues');
         }

         that.setData({
           isLoading: false,
           loadingMessage: '',
           taskId: taskId,
           logContent: formattedResponse,
           logType: 'success',
           showLog: true,
           logCount: 1
         });

         // Debug: Verify what was actually set
         setTimeout(() => {
           console.log('Actual logContent length after setData:', that.data.logContent.length);
           console.log('Actual logContent content:', that.data.logContent);
           console.log('Actual taskId after setData:', that.data.taskId);
           console.log('TaskId section should be visible:', !!that.data.taskId);
         }, 100);

         wx.showToast({
           title: 'Notification sent successfully',
           icon: 'success',
           duration: 3000
         });

         // Reset form after successful send
         setTimeout(() => {
           that.resetFormState();
         }, 1000);
       })
      .catch(error => {
        console.error('Failed to send notification:', error);

        // Safely format the error for display
        let formattedError = `Error: ${error.message || 'Unknown error'}\nCode: ${error.code || 'N/A'}\nFull Error: ${that.formatObjectForDisplay(error)}`;

        that.setData({
          isLoading: false,
          loadingMessage: '',
          logContent: formattedError,
          logType: 'error',
          showLog: true,
          logCount: 1
        });

        // Show user-friendly error message
        const userMessage = notificationService.getErrorMessage(error.code);
        wx.showToast({
          title: userMessage,
          icon: 'error',
          duration: 4000
        });
      });
  },

  /**
   * Navigate back to previous page
   */
  navigateBack: function() {
    wx.navigateBack();
  },

  /**
   * Log error to the error log textbox
   * @param {string} operation - The operation that caused the error
   * @param {Error|Object} error - The error object or error details
   * @param {Object} context - Additional context information
   */
  logErrorToTextbox: function(operation, error, context = {}) {
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });

    // Better error message extraction
    let errorMessage = 'Unknown error';
    if (error) {
      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error.message) {
        errorMessage = typeof error.message === 'string' ? error.message : JSON.stringify(error.message);
      } else if (error.errMsg) {
        errorMessage = error.errMsg;
      } else {
        try {
          errorMessage = JSON.stringify(error);
        } catch (e) {
          errorMessage = error.toString ? error.toString() : 'Error object could not be converted to string';
        }
      }
    }

    // Better error code extraction
    let errorCode = 'N/A';
    if (error) {
      if (error.code) {
        errorCode = error.code;
      } else if (error.errCode) {
        errorCode = error.errCode;
      }
    }

    // Enhanced error formatting for better readability
    let errorEntry = `[${timestamp}] ERROR in ${operation}:\n`;
    errorEntry += `Message: ${errorMessage}\n`;
    errorEntry += `Code: ${errorCode}\n`;

    if (context) {
      // Network-specific information
      if (context.networkInfo) {
        errorEntry += `Network Info: ${JSON.stringify(context.networkInfo, null, 2)}\n`;
      }

      // Request information for network errors
      if (context.requestInfo) {
        errorEntry += `Request Info: ${JSON.stringify(context.requestInfo, null, 2)}\n`;
      }

      // Notification data summary
      if (context.notificationData) {
        errorEntry += `Notification Data Summary: ${JSON.stringify(context.notificationData, null, 2)}\n`;
      }

      // General context (excluding already processed fields)
      const generalContext = { ...context };
      delete generalContext.networkInfo;
      delete generalContext.requestInfo;
      delete generalContext.notificationData;

      if (Object.keys(generalContext).length > 0) {
        errorEntry += `Additional Context: ${JSON.stringify(generalContext, null, 2)}\n`;
      }
    }

    // Full error object for technical debugging
    errorEntry += `Full Error Object: ${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}\n`;
    errorEntry += '----------------------------------------\n';

    const currentLog = this.data.errorLog || '';
    const newLog = currentLog + errorEntry;

    this.setData({
      errorLog: newLog,
      errorCount: this.data.errorCount + 1
    });

    // Auto-show error log if it's hidden
    if (!this.data.showErrorLog) {
      this.setData({
        showErrorLog: true
      });
    }

    console.error(`Error logged to textbox - Operation: ${operation}`, {
      originalError: error,
      processedMessage: errorMessage,
      processedCode: errorCode,
      context: context
    });
  },

  /**
   * Clear the log textbox
   */
  clearLog: function() {
    this.setData({
      logContent: '',
      logType: 'none',
      showLog: false,
      logCount: 0
    });
  },

  /**
   * Toggle log visibility
   */
  toggleLog: function() {
    this.setData({
      showLog: !this.data.showLog
    });
  },

  /**
   * Get formatted timestamp for logging
   */
  getTimestamp: function() {
    return new Date().toLocaleString();
  },

  /**
   * Get network information for debugging
   */
  getNetworkInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const networkType = wx.getNetworkType ? wx.getNetworkType() : { networkType: 'unknown' };

      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        networkType: networkType.networkType,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Failed to get network info:', error);
      return {
        error: 'Unable to retrieve network info',
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Format object for safe display in logs - matches expected DTO format
   * @param {any} obj - The object to format
   * @returns {string} Formatted string representation
   */
  formatObjectForDisplay: function(obj) {
    if (!obj || typeof obj !== 'object') {
      return String(obj);
    }

    try {
      // Try to format as DTO string to match expected output
      let dtoString = 'Response: NotificationResponseDTO(';
      const pairs = [];

      try {
        const keys = Object.keys(obj);
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          try {
            let value = obj[key];
            if (value === null || value === undefined) {
              value = 'null';
            } else if (typeof value === 'string') {
              value = value; // Keep strings as is
            } else {
              value = String(value); // Convert other types to string
            }
            pairs.push(`${key}=${value}`);
          } catch (e) {
            console.warn(`Failed to access property ${key}:`, e);
            pairs.push(`${key}=[Unable to access]`);
          }
        }
      } catch (e) {
        console.warn('Failed to get object keys:', e);
        dtoString = 'Response: NotificationResponseDTO([Unable to format object])';
        pairs.push('[Unable to format object]');
      }

      dtoString += pairs.join('; ') + ')';
      return dtoString;
    } catch (e) {
      console.warn('Failed to format as DTO:', e);
      // Fallback to JSON
      try {
        return JSON.stringify(obj, null, 2);
      } catch (e2) {
        console.warn('Failed to stringify object:', e2);
        let result = '{\n';
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            try {
              result += `  "${key}": ${JSON.stringify(obj[key])},\n`;
            } catch {
              result += `  "${key}": [Unable to stringify],\n`;
            }
          }
        }
        result += '}';
        return result;
      }
    }
  },

  /**
   * Test network connectivity before sending notification
   */
  testNetworkConnection: function() {
    const that = this;
    console.log('[NetworkTest] Starting connectivity test...');

    wx.showLoading({
      title: 'Testing connection...',
    });

    // Test basic connectivity with a simple request
    wx.request({
      url: 'https://www.baidu.com',
      method: 'HEAD',
      timeout: 5000,
      success: (res) => {
        console.log('[NetworkTest] Connectivity test successful:', {
          statusCode: res.statusCode,
          headers: res.header,
          timestamp: new Date().toISOString()
        });

        wx.hideLoading();
        wx.showModal({
          title: 'Network Test Result',
          content: `✅ Network connection is working\nStatus: HTTP ${res.statusCode}\n\nYou can try sending the notification again.`,
          showCancel: false,
          confirmText: 'OK'
        });
      },
      fail: (err) => {
        console.error('[NetworkTest] Connectivity test failed:', err);

        wx.hideLoading();
        wx.showModal({
          title: 'Network Test Result',
          content: `❌ Network connection issue detected\nError: ${err.errMsg}\n\nPlease check your internet connection and try again.`,
          showCancel: false,
          confirmText: 'OK'
        });

        // Log network test failure
        that.logErrorToTextbox('networkTest', err, {
          testType: 'connectivity',
          url: 'https://www.baidu.com',
          timestamp: new Date().toISOString()
        });
      }
    });
  },

  /**
   * Debug function to check current form state
   */
  debugFormState: function() {
    const { targetPushId, pushMessagesTc, pushMessagesSc, pushMessagesEn, canSend } = this.data;

    console.log('=== FORM DEBUG INFO ===');
    console.log('targetPushId:', targetPushId, 'Length:', targetPushId.length);
    console.log('pushMessagesTc:', pushMessagesTc, 'Length:', pushMessagesTc.length);
    console.log('pushMessagesSc:', pushMessagesSc, 'Length:', pushMessagesSc.length);
    console.log('pushMessagesEn:', pushMessagesEn, 'Length:', pushMessagesEn.length);
    console.log('canSend:', canSend);

    // Force re-validation
    this.validateForm();

    wx.showToast({
      title: 'Debug info logged to console',
      icon: 'none',
      duration: 2000
    });
  }
});