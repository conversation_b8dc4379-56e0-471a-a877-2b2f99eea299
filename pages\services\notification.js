// Notification service for iAM Smart API integration
// Provides centralized API integration and utilities for the notification feature

class NotificationService {
  constructor() {
    this.baseURL = 'https://dev.dapdapdap.online/testiamsmart/eservice/v1/api/sendNotification';
    this.projectConfig = {
      messageId: "ABCD1234",
      miniProgramId: "mpyhkysqoik9t14q",
      launchPath: "/pages/notification/read_noti"
    };
  }

  async sendNotification(notificationData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.baseURL,
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          ...this.projectConfig,
          ...notificationData
        },
        timeout: 15000,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(this.createError('API_ERROR', `HTTP ${res.statusCode}: ${res.data?.message || 'Unknown error'}`));
          }
        },
        fail: (err) => {
          reject(this.createError('NETWORK_ERROR', 'Network request failed'));
        }
      });
    });
  }

  createError(code, message, context = {}) {
    return {
      code,
      message,
      timestamp: new Date().toISOString(),
      context
    };
  }

  getErrorMessage(errorCode) {
    const errorMessages = {
      'API_ERROR': 'Failed to send notification. Please try again.',
      'NETWORK_ERROR': 'Network error. Please check your connection.',
      'VALIDATION_ERROR': 'Please check your input and try again.'
    };
    return errorMessages[errorCode] || 'An unexpected error occurred. Please try again.';
  }

  validatePushId(pushId) {
    if (!pushId || typeof pushId !== 'string') {
      return false;
    }
    return /^[a-zA-Z0-9]{32}$/.test(pushId);
  }

  async getNotifications(pushId) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://dev.dapdapdap.online/testiamsmart/eservice/v1/api/getNotifications',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          pushId: pushId,
          ...this.projectConfig
        },
        timeout: 10000,
        success: (res) => {
          if (res.statusCode === 200) {
            let notifications = [];
            if (Array.isArray(res.data)) {
              notifications = res.data;
            } else if (res.data && Array.isArray(res.data.notifications)) {
              notifications = res.data.notifications;
            } else if (res.data && res.data.content) {
              notifications = res.data.content;
            }
            resolve(notifications);
          } else {
            reject(this.createError('API_ERROR', `HTTP ${res.statusCode}: Failed to get notifications`));
          }
        },
        fail: (err) => {
          reject(this.createError('NETWORK_ERROR', 'Failed to retrieve notifications'));
        }
      });
    });
  }

  async updateNotificationStatus(notificationId, isRead) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://dev.dapdapdap.online/testiamsmart/eservice/v1/api/updateNotificationStatus',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          notificationId,
          isRead,
          ...this.projectConfig
        },
        timeout: 10000,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(this.createError('API_ERROR', `HTTP ${res.statusCode}: Failed to update status`));
          }
        },
        fail: (err) => {
          reject(this.createError('NETWORK_ERROR', 'Failed to update notification status'));
        }
      });
    });
  }

  async deleteNotification(notificationId) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://dev.dapdapdap.online/testiamsmart/eservice/v1/api/deleteNotification',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          notificationId,
          ...this.projectConfig
        },
        timeout: 10000,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(this.createError('API_ERROR', `HTTP ${res.statusCode}: Failed to delete notification`));
          }
        },
        fail: (err) => {
          reject(this.createError('NETWORK_ERROR', 'Failed to delete notification'));
        }
      });
    });
  }

  logError(operation, error, context = {}) {
    console.error(`[NotificationService:${operation}]`, {
      timestamp: new Date().toISOString(),
      operation,
      error: {
        code: error.code || 'UNKNOWN',
        message: error.message || 'No message provided'
      },
      context
    });
  }
}

module.exports = new NotificationService();