# Speckit and development files
speckit.txt
.roo/*
.specify/*
specs/*
*.md
wx-esdemo

# WeChat Mini Program specific
project.private.config.json
node_modules/
dist/
.unpackage/
coverage/

# Build outputs
*.min.js
*.min.wxml
*.min.wxss

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp

# WeChat Developer Tools
wechat-dev-tools-*

# Project specific
images/upgrade_images/Add_Noti_Icon.jpg
iAM Smart Mini-program Notification API (Draft) (20250910).pdf
