Page({
  data: {
    topics: [
      {
        title: 'Travelling',
        icon: '/images/travelling.svg',
        titleLoggedIn: '貿易及營商',
        iconLoggedIn: '/images/trade-business.svg'
      },
      {
        title: 'Newborn Parent',
        icon: '/images/newborn.svg',
        titleLoggedIn: '稅務',
        iconLoggedIn: '/images/taxation.svg'
      },
      {
        title: 'Kindergarten Admission',
        icon: '/images/kindergarten.svg',
        titleLoggedIn: '註冊及牌照',
        iconLoggedIn: '/images/company-registration.svg'
      },
      {
        title: 'Info for Elderly',
        icon: '/images/elderly.svg',
        titleLoggedIn: '市場資訊及統計數據',
        iconLoggedIn: '/images/market-data.svg'
      }
    ],
    globalFontSize: 'default',
    isLoggedIn: false
  },
  
  onLoad: function() {
    // Load global font size
    const app = getApp();
    const isLoggedIn = app.globalData.userInfo && app.globalData.userInfo.isLoggedIn;
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: isLoggedIn
    });

    // Initialize topic icons based on login status
    this.updateTopicIcons(isLoggedIn);
  },
  
  onShow: function() {
    // Update font size when returning to page
    const app = getApp();
    const isLoggedIn = app.globalData.userInfo && app.globalData.userInfo.isLoggedIn;
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: isLoggedIn
    });

    // Update topic icons based on current login status
    this.updateTopicIcons(isLoggedIn);
  },
  
  updateTopicIcons: function (isLoggedIn) {
    console.log("=== UPDATING TOPIC ICONS ===");
    console.log("Login status:", isLoggedIn);
    console.log("Current topics data:", this.data.topics);

    const updatedTopics = this.data.topics.map((topic, index) => {
      const currentIcon = isLoggedIn ? topic.iconLoggedIn : topic.icon;
      const currentTitle = isLoggedIn ? topic.titleLoggedIn : topic.title;
      console.log(`Topic ${index}:`);
      console.log(`  Original title: ${topic.title}`);
      console.log(`  Logged-in title: ${topic.titleLoggedIn}`);
      console.log(`  Current title: ${currentTitle}`);
      console.log(`  Original icon: ${topic.icon}`);
      console.log(`  Logged-in icon: ${topic.iconLoggedIn}`);
      console.log(`  Current icon: ${currentIcon}`);

      return {
        ...topic,
        currentIcon: currentIcon,
        currentTitle: currentTitle
      };
    });

    console.log("Final updated topics:", updatedTopics);

    this.setData({
      topics: updatedTopics
    });

    console.log("setData completed for topics");
  },

  navigateBack: function() {
    wx.navigateBack();
  },

  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: Get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  }
})


