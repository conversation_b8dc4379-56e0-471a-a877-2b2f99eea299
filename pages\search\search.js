Page({
  data: {
    searchQuery: '',
    showKeyboard: false,
    hotServices: [
      { title: 'eTraffic Ticket Platform', favorite: false },
      { title: 'Online Voter Information Enquiry', favorite: true },
      { title: 'SmartPLAY', favorite: false },
      { title: 'eTAX - Individual Tax Portal', favorite: true },
      { title: 'Renew Vehicle Licence', favorite: false },
      { title: 'International Driving Permit', favorite: false }
    ],
    globalFontSize: 'default',
    isLoggedIn: false
  },
  
  onLoad: function() {
    // Load global font size
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onShow: function() {
    // Update font size when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onSearchInput: function(e) {
    this.setData({
      searchQuery: e.detail.value
    });
  },
  
  toggleFavorite: function(e) {
    const index = e.currentTarget.dataset.index;
    const favoriteKey = `hotServices[${index}].favorite`;
    const newValue = !this.data.hotServices[index].favorite;
    
    this.setData({
      [favoriteKey]: newValue
    });
  },
  
  navigateBack: function() {
    wx.navigateBack();
  },
  
  onSearch: function() {
    // To Do: Implement search functionality
    console.log('Searching for:', this.data.searchQuery);
  },
  
  // Bottom navigation handlers
  navigateToHome: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },
  
  navigateToServices: function() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },
  
  onScanTap: function() {
    // Temporarily disabled due to scanner crashes
    wx.showToast({
      title: 'Scan feature temporarily unavailable',
      icon: 'none'
    });
    
    /*
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('QR Code scan result:', res);
        this.handleQRCodeResult(res.result);
      },
      fail: (err) => {
        console.log('QR scan failed:', err);
        wx.showToast({
          title: 'QR scan cancelled',
          icon: 'none'
        });
      }
    });
    */
  },
  
  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: Get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  },
  
  navigateToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },
  
  /*
  handleQRCodeResult: function(result) {
    if (result.includes('corpid') || result.includes('personal-code')) {
      wx.navigateTo({
        url: '/pages/personal-code/personal-code?code=' + encodeURIComponent(result)
      });
    } else {
      wx.showModal({
        title: 'QR Code Scanned',
        content: result,
        showCancel: false
      });
    }
  },
  */
})




