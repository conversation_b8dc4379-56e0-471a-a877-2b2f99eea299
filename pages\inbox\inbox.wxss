.container {
  background-color: #ffffff;
  min-height: 100vh;
  padding-bottom: 110rpx; /* Add padding to account for the tab bar */
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
  border-bottom: none !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.settings-button {
  padding: 10rpx;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
}

.tab {
  flex: 1;
  padding: 30rpx 0;
  text-align: center;
  font-size: 32rpx;
  color: #999999;
  position: relative;
}

.tab.active {
  color: #006633;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 6rpx;
  background-color: #006633;
  border-radius: 3rpx;
}

.notification-dot {
  position: absolute;
  top: 20rpx;
  right: 20%;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff0000;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  background-color: #e8f5f0;
  border-radius: 50%;
  padding: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
}

.notification-settings {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #e0e0e0;
}

.settings-text {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #006633;
}

.message-list {
  background-color: white;
}

.message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #e0e0e0;
}

.message-content {
  flex: 1;
}

.message-title {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.message-date {
  font-size: 26rpx;
  color: #999999;
  display: block;
}

.message-actions {
  display: flex;
  align-items: center;
}

.unread-indicator {
  width: 16rpx;
  height: 16rpx;
  background-color: #ff0000;
  border-radius: 50%;
  margin-right: 20rpx;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 28rpx !important;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -10rpx;
  margin-bottom: -6rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}

/* Font size classes */
.font-small .header-title {
  font-size: 32rpx;
}

.font-small .tab {
  font-size: 28rpx;
}

.font-small .settings-text {
  font-size: 28rpx;
}

.font-small .message-title {
  font-size: 28rpx;
}

.font-small .message-date {
  font-size: 22rpx;
}

.font-small .empty-text {
  font-size: 28rpx;
}

.font-default .header-title {
  font-size: 36rpx;
}

.font-default .tab {
  font-size: 32rpx;
}

.font-default .settings-text {
  font-size: 32rpx;
}

.font-default .message-title {
  font-size: 32rpx;
}

.font-default .message-date {
  font-size: 26rpx;
}

.font-default .empty-text {
  font-size: 32rpx;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .tab {
  font-size: 36rpx;
}

.font-large .settings-text {
  font-size: 36rpx;
}

.font-large .message-title {
  font-size: 36rpx;
}

.font-large .message-date {
  font-size: 30rpx;
}

.font-large .empty-text {
  font-size: 36rpx;
}

/* Font size classes for navigation */
.font-small .nav-text,
.font-default .nav-text,
.font-large .nav-text,
.bottom-nav .font-small .nav-text,
.font-small .bottom-nav .nav-text,
.bottom-nav .font-default .nav-text,
.font-default .bottom-nav .nav-text,
.bottom-nav .font-large .nav-text,
.font-large .bottom-nav .nav-text {
  font-size: 28rpx !important;
}

