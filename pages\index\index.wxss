.container {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  padding-bottom: 110rpx;
  padding-top: 0; /* Remove custom top padding */
}

/* Banner styles */
.banner {
  position: relative;
  width: 100%;
  height: 260rpx;
  background: linear-gradient(rgba(200,200,200,0.7), rgba(200,200,200,0.7));
  background-size: cover;
  display: flex;
  justify-content: space-between;
  padding: 30rpx 40rpx 20rpx 40rpx;
  box-sizing: border-box;
}

/* Orange theme for logged in users */
.logged-in-theme .banner {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
}

.banner-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  white-space: nowrap;
  line-height: 1.4;
}

.subtitle {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.login {
  font-size: 28rpx;
  color: white;
  margin-top: 10rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 15rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: inline-block;
  text-align: center;
  min-width: 40rpx;
  max-width: 200rpx;
  width: auto;
}

/* Font size classes for login button */
.font-small .login {
  font-size: 24rpx;
  padding: 3rpx 6rpx;
}

.font-default .login {
  font-size: 28rpx;
  padding: 4rpx 8rpx;
}

.font-large .login {
  font-size: 32rpx;
  padding: 5rpx 9rpx;
}

.banner-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-shrink: 0;
  margin-left: 20rpx;
  margin-top: 80rpx;
}

.weather-box {
  display: flex;
  align-items: center;
  background-color: rgba(128, 128, 128, 0.7);
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}

.weather-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
}

.temperature {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.alert-icons {
  display: flex;
  margin-top: 20rpx;
}

.alert-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
}

/* Navigation Bar */
.nav-bar {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.nav-bar .nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-right: 1px solid #e0e0e0;
}

.nav-bar .nav-item:last-child {
  border-right: none;
}

.nav-bar .nav-text {
  font-size: 28rpx;
  color: #333;
  margin-top: 10rpx;
}

.nav-bar .nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

/* Alert Message */
.alert-message {
  padding: 20rpx;
  color: #006633;
  font-size: 28rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

/* Tab Section */
.tab-section {
  display: flex;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  margin-top: 20rpx;
}

.tab {
  padding: 10rpx 20rpx;
  font-size: 30rpx;
  color: #333;
}

.tab.active {
  font-weight: bold;
  border-bottom: 3px solid #006633;
}

.more {
  margin-left: auto;
  color: #006633;
  font-size: 28rpx;
}

/* Services Grid */
.services-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: white;
}

.service-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.service-title {
  font-size: 24rpx;
  text-align: center;
  color: #333;
  max-width: 90%;
}

/* Topics Grid */
.topics-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: white;
}

.topic-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.topic-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.topic-title {
  font-size: 24rpx;
  text-align: center;
  color: #333;
  max-width: 90%;
}

/* Useful Information */
.info-section {
  margin-top: 20rpx;
  background-color: white;
  padding: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.update-time {
  color: #006633;
  font-size: 26rpx;
}

.harbour-section {
  margin-top: 20rpx;
  background-color: white;
  padding: 20rpx 30rpx;
}

.harbour-info {
  display: flex;
  overflow-x: auto;
  padding-bottom: 20rpx;
}

.harbour-card {
  min-width: 300rpx;
  background-color: #003366;
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.harbour-title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.harbour-location {
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.harbour-speeds {
  display: flex;
  margin-bottom: 20rpx;
}

.speed-item {
  margin-right: 30rpx;
  text-align: center;
}

.speed-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #4CAF50;
  display: block;
}

.speed-unit {
  font-size: 24rpx;
  color: #4CAF50;
}

.harbour-destination {
  font-size: 26rpx;
  text-align: center;
}

.harbour-fees {
  display: flex;
  justify-content: space-around;
  margin-top: 10rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 10rpx;
}

.fee-item {
  font-size: 24rpx;
  color: white;
}

/* Bottom Navigation - with special scan button */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.bottom-nav .nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.bottom-nav .nav-item.active {
  color: #006633;
}

.bottom-nav .nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.bottom-nav .nav-text {
  font-size: 22rpx;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -10rpx;
  margin-bottom: -6rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}

/* News ticker with smooth horizontal scrolling */
.news-ticker {
  position: relative;
  width: 100%;
  height: 80rpx;
  background-color: white;
  overflow: hidden;
  border-bottom: 1px solid #e0e0e0;
}

.news-ticker-content {
  display: flex;
  height: 100%;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.news-headline {
  min-width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  color: #006633;
  font-size: 28rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Remove the old news-section styles */
.news-section {
  display: none;
}

/* Font size classes for homepage */
.font-small .title {
  font-size: 28rpx;
}

.font-small .subtitle {
  font-size: 28rpx;
}

.font-small .login,
.font-small .temperature,
.font-small .nav-text,
.font-small .news-headline,
.font-small .tab,
.font-small .more,
.font-small .service-title,
.font-small .harbour-title,
.font-small .harbour-location,
.font-small .harbour-destination {
  font-size: 26rpx;
}

.font-small .speed-value {
  font-size: 34rpx;
}

.font-small .speed-unit,
.font-small .fee-item {
  font-size: 20rpx;
}

.font-default .title {
  font-size: 32rpx;
}

.font-default .subtitle {
  font-size: 32rpx;
}

.font-default .login,
.font-default .temperature,
.font-default .nav-text,
.font-default .news-headline,
.font-default .tab,
.font-default .more,
.font-default .service-title,
.font-default .harbour-title,
.font-default .harbour-location,
.font-default .harbour-destination {
  font-size: 28rpx;
}

.font-default .speed-value {
  font-size: 40rpx;
}

.font-default .speed-unit,
.font-default .fee-item {
  font-size: 24rpx;
}

.font-large .title {
  font-size: 36rpx;
}

.font-large .subtitle {
  font-size: 36rpx;
}

.font-large .login,
.font-large .temperature,
.font-large .nav-text,
.font-large .news-headline,
.font-large .tab,
.font-large .more,
.font-large .service-title,
.font-large .harbour-title,
.font-large .harbour-location,
.font-large .harbour-destination {
  font-size: 32rpx;
}

.font-large .speed-value {
  font-size: 46rpx;
}

.font-large .speed-unit,
.font-large .fee-item {
  font-size: 28rpx;
}

.font-small .service-title,
.font-small .topic-title,
.font-small .harbour-title,
.font-small .harbour-location,
.font-small .harbour-destination {
  font-size: 26rpx;
}

.font-default .service-title,
.font-default .topic-title,
.font-default .harbour-title,
.font-default .harbour-location,
.font-default .harbour-destination {
  font-size: 28rpx;
}

.font-large .service-title,
.font-large .topic-title,
.font-large .harbour-title,
.font-large .harbour-location,
.font-large .harbour-destination {
  font-size: 32rpx;
}

/* Lite Mode Styles */
.lite-todo-section {
  margin: 20rpx;
  background-color: white;
  border: 2rpx solid #ff4444;
  border-radius: 20rpx;
  padding: 30rpx;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.todo-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.todo-text {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

.lite-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 20rpx;
}

.lite-action-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 2rpx solid #e0e0e0;
}

.lite-action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.lite-action-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.inbox-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: #ff4444;
  color: white;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.lite-recent-section {
  margin: 20rpx;
}

.lite-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.lite-section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.lite-more-link {
  font-size: 28rpx;
  color: #006633;
}

.lite-recent-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.lite-recent-item {
  background-color: #f5f5f5;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
}

.lite-recent-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

/* Lite Mode Main Actions */
.lite-main-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 40rpx 20rpx;
}

.lite-action-button {
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 160rpx;
}

.lite-main-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.lite-main-text {
  font-size: 36rpx;
  color: #2d7d6b;
  font-weight: 500;
}

/* Featured Services Section */
.lite-featured-section {
  margin: 40rpx 20rpx;
}

.lite-featured-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.lite-featured-title {
  font-size: 40rpx;
  color: #333333;
  font-weight: 600;
}

.lite-more-link {
  font-size: 32rpx;
  color: #2d7d6b;
  font-weight: 500;
}

.lite-featured-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.lite-featured-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.lite-featured-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.3;
}

.user-names {
  font-size: 28rpx;
  font-weight: normal;
  color: white;
  margin-top: 5rpx;
}

.company-name {
  font-size: 28rpx;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10rpx;
  opacity: 0.9;
}

/* Font size classes for user names */
.font-small .user-names {
  font-size: 24rpx;
}

.font-default .user-names {
  font-size: 28rpx;
}

.font-large .user-names {
  font-size: 32rpx;
}

/* Font size classes for company name */
.font-small .company-name {
  font-size: 24rpx;
}

.font-default .company-name {
  font-size: 28rpx;
}

.font-large .company-name {
  font-size: 32rpx;
}





