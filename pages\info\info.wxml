<view class="container font-{{globalFontSize}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Info</text>
  </view>
  
  <view class="info-grid">
    <view class="info-item" wx:for="{{infoItems}}" wx:key="index" bindtap="onItemTap" data-index="{{index}}">
      <image src="{{item.icon}}" class="info-icon"></image>
      <text class="info-title">{{item.title}}</text>
    </view>
  </view>
  
  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>








