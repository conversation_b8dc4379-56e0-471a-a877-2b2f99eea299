Page({
  data: {
    globalFontSize: 'default',
    liteMode: false
  },
  
  onLoad: function() {
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false
    });
  },
  
  onShow: function() {
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false
    });
  },
  
  navigateBack: function() {
    wx.navigateBack();
  }
})