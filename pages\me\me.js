Page({
  data: {
    activeTab: 'profiles',
    disclaimer: 'Users must agree to allow "iAM Smart" to obtain the personal information including Bills, Balances, To-do Lists and Application Status, through a system managed by the Digital Policy Office (DPO).',
    globalFontSize: 'default',
    liteMode: false,
    currentSlideIndex: 0,
    slides: [
      { id: 1, image: '/images/assistant-slide1.png' },
      { id: 2, image: '/images/assistant-slide2.png' },
      { id: 3, image: '/images/assistant-slide3.png' }
    ],
    profileItems: [
      {
        title: 'Personal Code',
        description: '',
        icon: '/images/personal-code.svg',
        background: '#FFF8E1'
      },
      {
        title: 'Contactless e-Channel',
        description: '',
        icon: '/images/echannel.svg',
        background: '#E3F2FD'
      },
      {
        title: 'Digital Document',
        description: 'Please set up your digital document',
        icon: '',
        background: '#00897B',
        hasAddButton: true
      },
      {
        title: 'e-ME Profile',
        description: 'Please fill in your profile',
        icon: '',
        background: '#F5F5F5',
        hasArrow: true
      }
    ],
    // Touch tracking for swipe
    touchStartX: 0,
    touchEndX: 0,
    isLoggedIn: false
  },
  
  onLoad: function() {
    // Load global font size and lite mode
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onShow: function() {
    // Update font size and lite mode when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  
  handleTouchStart: function(e) {
    this.setData({
      touchStartX: e.touches[0].clientX
    });
  },
  
  handleTouchMove: function(e) {
    this.setData({
      touchEndX: e.touches[0].clientX
    });
  },
  
  handleTouchEnd: function() {
    const { touchStartX, touchEndX } = this.data;
    const minSwipeDistance = 50; // Minimum distance to be considered a swipe
    
    if (touchStartX - touchEndX > minSwipeDistance) {
      // Swiped left - go to next tab
      this.switchToNextTab();
    } else if (touchEndX - touchStartX > minSwipeDistance) {
      // Swiped right - go to previous tab
      this.switchToPrevTab();
    }
  },
  
  switchToNextTab: function() {
    const tabs = ['profiles', 'todo', 'status'];
    const currentIndex = tabs.indexOf(this.data.activeTab);
    
    if (currentIndex < tabs.length - 1) {
      this.setData({
        activeTab: tabs[currentIndex + 1]
      });
    }
  },
  
  switchToPrevTab: function() {
    const tabs = ['profiles', 'todo', 'status'];
    const currentIndex = tabs.indexOf(this.data.activeTab);
    
    if (currentIndex > 0) {
      this.setData({
        activeTab: tabs[currentIndex - 1]
      });
    }
  },
  
  prevSlide: function() {
    let newIndex = this.data.currentSlideIndex - 1;
    if (newIndex < 0) {
      newIndex = this.data.slides.length - 1;
    }
    
    this.setData({
      currentSlideIndex: newIndex
    });
  },
  
  nextSlide: function() {
    let newIndex = this.data.currentSlideIndex + 1;
    if (newIndex >= this.data.slides.length) {
      newIndex = 0;
    }
    
    this.setData({
      currentSlideIndex: newIndex
    });
  },
  
  goToSlide: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentSlideIndex: index
    });
  },
  
  addAssistant: function() {
    console.log('Add assistant clicked');
    // Implement add functionality
  },
  
  handleProfileItemTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.profileItems[index];
    console.log('Profile item tapped:', item.title);
    // Clicking will not navigate anywhere
  },
  navigateBack: function() {
    wx.navigateBack();
  },
  // Bottom navigation handlers
  navigateToHome: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },
  
  navigateToServices: function() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },
  
  onScanTap: function() {
    // Temporarily disabled due to scanner crashes
    wx.showToast({
      title: 'Scan feature temporarily unavailable',
      icon: 'none'
    });
    
    /*
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('QR Code scan result:', res);
        this.handleQRCodeResult(res.result);
      },
      fail: (err) => {
        console.log('QR scan failed:', err);
        wx.showToast({
          title: 'QR scan cancelled',
          icon: 'none'
        });
      }
    });
    */
  },
  
  /*
  handleQRCodeResult: function(result) {
    if (result.includes('corpid') || result.includes('personal-code')) {
      wx.navigateTo({
        url: '/pages/personal-code/personal-code?code=' + encodeURIComponent(result)
      });
    } else {
      wx.showModal({
        title: 'QR Code Scanned',
        content: result,
        showCancel: false
      });
    }
  },
  */
  
  navigateToMe: function() {
    // Already on me page
    console.log('Already on me page');
  },
  
  navigateToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  }
})












