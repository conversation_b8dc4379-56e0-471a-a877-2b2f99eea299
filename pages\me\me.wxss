.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 140rpx;
}

.header {
  padding: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  position: relative;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  position: absolute;
  left: 30rpx;
  padding: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.refresh-button {
  position: absolute;
  right: 30rpx;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #006633;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background-color: #006633;
}

/* Disclaimer Banner */
.disclaimer-banner {
  padding: 20rpx 30rpx;
  background-color: #e0f2e9;
  margin: 20rpx;
  border-radius: 8rpx;
}

.disclaimer-banner text {
  font-size: 28rpx;
  color: #006633;
  line-height: 1.5;
}

/* Font size classes for disclaimer */
.font-small .disclaimer-banner text {
  font-size: 24rpx;
}

.font-default .disclaimer-banner text {
  font-size: 28rpx;
}

.font-large .disclaimer-banner text {
  font-size: 32rpx;
}

/* Tab Content Container with Swipe */
.tab-content-container {
  flex: 1;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.tab-content {
  width: 100%;
  min-height: 400rpx; /* Ensure minimum height for content */
}

/* Animation for tab transitions */
.tab-content {
  transition: transform 0.3s ease;
}

/* Profiles Content */
.profiles-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.profile-row {
  display: flex;
  gap: 20rpx;
}

.profile-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  flex: 1;
  min-height: 180rpx;
  display: flex;
  align-items: center;
}

.profile-card.full-width {
  width: 100%;
}

.profile-card-content {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.profile-info {
  display: flex;
  align-items: center;
}

.profile-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.profile-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #CD8500;
}

.profile-title.white {
  color: white;
}

.profile-title.green {
  color: #006633;
}

.profile-description {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}

.profile-description.white {
  color: white;
}

.profile-description.green {
  color: #006633;
}

.profile-text-container {
  display: flex;
  flex-direction: column;
}

.add-circle {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

.arrow-circle {
  width: 60rpx;
  height: 60rpx;
  background-color: #00897B;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.arrow-icon {
  color: white;
  font-size: 30rpx;
  font-weight: bold;
}

.empty-text {
  color: #999999;
  font-size: 30rpx;
}

/* Carousel/Slider */
.carousel {
  position: relative;
  width: 100%;
  height: 600rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.carousel-container {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
}

.carousel-slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide-image {
  width: 80%;
  height: 80%;
}

.carousel-nav-left, .carousel-nav-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #006633;
  font-size: 60rpx;
  font-weight: bold;
}

.carousel-nav-left {
  left: 20rpx;
}

.carousel-nav-right {
  right: 20rpx;
}

.carousel-dots {
  position: absolute;
  bottom: 30rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10rpx;
}

.carousel-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #cccccc;
}

.carousel-dot.active {
  background-color: #006633;
}

/* Add Button */
.add-button {
  background-color: #006633;
  color: white;
  padding: 20rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
}

/* Lite Mode Styles */
.lite-mode .tab-navigation {
  border-bottom: 1px solid #e0e0e0;
}

.lite-mode .tab.active::after {
  height: 4rpx;
}

.lite-disclaimer-box {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #e8f5e8;
  border-radius: 15rpx;
}

.lite-disclaimer-text {
  font-size: 38rpx;
  color: #006633;
  line-height: 1.5;
}

/* Font size variations for lite disclaimer */
.font-small .lite-disclaimer-text {
  font-size: 34rpx;
}

.font-default .lite-disclaimer-text {
  font-size: 38rpx;
}

.font-large .lite-disclaimer-text {
  font-size: 42rpx;
}

.lite-phone-container {
  margin: 40rpx 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lite-phone-image {
  width: 80%;
  max-height: 600rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.lite-add-button {
  margin: 20rpx;
  padding: 30rpx 0;
  background-color: #006633;
  color: white;
  text-align: center;
  border-radius: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 28rpx !important;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 60rpx;
  height: 60rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.scan-icon {
  width: 30rpx;
  height: 30rpx;
}

/* Font size classes for navigation */
.font-small .nav-text {
  font-size: 20rpx;
}

.font-default .nav-text {
  font-size: 22rpx;
}

.font-large .nav-text {
  font-size: 24rpx;
}

/* Font size classes for navigation - more specific selectors */
.bottom-nav .font-small .nav-text,
.font-small .bottom-nav .nav-text {
  font-size: 20rpx !important;
}

.bottom-nav .font-default .nav-text,
.font-default .bottom-nav .nav-text {
  font-size: 22rpx !important;
}

.bottom-nav .font-large .nav-text,
.font-large .bottom-nav .nav-text {
  font-size: 22rpx !important;
}

/* Override navigation text size to match index page */
.nav-text {
  font-size: 22rpx !important;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 60rpx;
  height: 60rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.scan-icon {
  width: 30rpx;
  height: 30rpx;
}









