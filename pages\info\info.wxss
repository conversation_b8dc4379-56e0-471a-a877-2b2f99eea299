.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
  border-bottom: none !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.info-item {
  width: calc(50% - 10rpx);
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f0f5f2;
  margin-bottom: 20rpx;
  padding: 30rpx 20rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.info-item:nth-child(odd) {
  margin-right: 10rpx;
}

.info-item:nth-child(even) {
  margin-left: 10rpx;
}

.info-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.info-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 28rpx;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 60rpx;
  height: 60rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.scan-icon {
  width: 30rpx;
  height: 30rpx;
}

/* Font size classes for navigation */
.font-small .nav-text {
  font-size: 26rpx;
}

.font-default .nav-text {
  font-size: 28rpx;
}

.font-large .nav-text {
  font-size: 30rpx;
}




