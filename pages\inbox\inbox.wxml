<view class="container page-container font-{{globalFontSize}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Inbox</text>
    <view class="settings-button" bindtap="openSettings">
      <icon type="list" size="24"></icon>
    </view>
  </view>
  
  <view class="tabs">
    <view class="tab {{activeTab === 'announcements' ? 'active' : ''}}" bindtap="switchTab" data-tab="announcements">
      <text>Announcements</text>
    </view>
    <view class="tab {{activeTab === 'messages' ? 'active' : ''}}" bindtap="switchTab" data-tab="messages">
      <text>Messages ({{messageCount}})</text>
      <view class="notification-dot" wx:if="{{messageCount > 0}}"></view>
    </view>
  </view>
  
  <!-- Announcements Tab Content -->
  <view wx:if="{{activeTab === 'announcements'}}">
    <view class="empty-state">
      <text class="empty-text">No announcements</text>
    </view>
  </view>
  
  <!-- Messages Tab Content -->
  <view wx:if="{{activeTab === 'messages'}}">
    <view class="notification-settings" bindtap="openNotificationSettings">
      <icon type="info" size="20" color="#006633"></icon>
      <text class="settings-text">Notification Settings</text>
    </view>
    
    <view class="message-list">
      <view class="message-item" wx:for="{{messages}}" wx:key="index" bindtap="openMessage" data-index="{{index}}">
        <view class="message-content">
          <text class="message-title">{{item.title}}</text>
          <text class="message-date">{{item.date}}</text>
        </view>
        <view class="message-actions">
          <view class="unread-indicator" wx:if="{{item.unread}}"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>



