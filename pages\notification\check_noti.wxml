<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Check Notification</text>
  </view>

  <!-- Content -->
  <view class="content">
    <view class="form-group">
      <text class="form-label">Task ID:</text>
      <input class="form-input" value="{{taskId}}" bindinput="onTaskIdInput" placeholder="Enter Task ID to check status"></input>
    </view>

    <button class="primary-button" bindtap="checkNotification" disabled="{{!taskId || isLoading}}">
      {{isLoading ? 'Checking...' : 'Check Status'}}
    </button>

    <view class="status-section" wx:if="{{notificationStatus}}">
      <text class="status-title">Notification Status:</text>
      <textarea class="status-text" value="{{notificationStatus}}" disabled="true" placeholder="Status will appear here..."></textarea>
    </view>

    <view class="error-section" wx:if="{{errorMessage}}">
      <text class="error-title">Error:</text>
      <text class="error-text">{{errorMessage}}</text>
    </view>
  </view>

  <!-- Loading Overlay -->
  <view class="loading-overlay {{isLoading ? 'visible' : 'hidden'}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">Checking notification status...</text>
  </view>
</view>