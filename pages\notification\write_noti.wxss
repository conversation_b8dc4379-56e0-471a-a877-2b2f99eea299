/* Notification Page Styles */
.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  position: relative;
}

.back-button {
  padding: 10rpx;
  margin-right: 20rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.content {
  flex: 1;
  padding: 20rpx;
}

/* Section Styles */
.section {
  background-color: #ffffff;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* PushID Section */
.pushid-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.primary-button {
  background-color: #006633;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}

.primary-button:active {
  background-color: #004d22;
  opacity: 0.8;
}

.pushid-display {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  opacity: 0;
  max-height: 0;
  transition: all 0.3s ease;
}

.pushid-display.visible {
  opacity: 1;
  max-height: 200rpx;
}

.pushid-display.hidden {
  opacity: 0;
  max-height: 0;
}

.copy-button-container {
  display: flex;
  justify-content: center;
  opacity: 0;
  max-height: 0;
  transition: all 0.3s ease;
}

.copy-button-container.visible {
  opacity: 1;
  max-height: 100rpx;
}

.copy-button-container.hidden {
  opacity: 0;
  max-height: 0;
}

.pushid-input {
  flex: 1;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
  background-color: #f8f8f8;
  color: #333333;
}

.pushid-input.selectable {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  border-color: #006633;
  background-color: #f0f8f0;
  cursor: text;
}

.copy-hint {
  font-size: 22rpx;
  color: #006633;
  text-align: center;
  margin-top: 10rpx;
  font-style: italic;
}

.copy-button {
  background-color: #f0f0f0;
  color: #666666;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-button:active {
  background-color: #e0e0e0;
}

/* Notification Form */
.notification-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.form-label.optional {
  color: #666666;
}

.form-label .required {
  color: #ff4444;
  font-weight: bold;
}

.form-hint {
  font-size: 24rpx;
  color: #999999;
  font-style: italic;
}

.form-input,
.form-textarea {
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  color: #333333;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #006633;
  outline: none;
}

.form-input {
  height: 80rpx;
}

.form-textarea {
  height: 50rpx !important;
  min-height: 50rpx !important;
  max-height: 50rpx !important;
  resize: none;
  overflow: hidden;
}

.char-counter {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin-top: 5rpx;
}

/* Language Tabs for Detail Messages */
.language-tabs {
  display: flex;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.tab-button {
  flex: 1;
  background-color: #f5f5f5;
  color: #666666;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 15rpx 10rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-button.active {
  background-color: #006633;
  color: #ffffff;
  border-color: #006633;
}

/* Button Row */
.button-row {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.secondary-button {
  background-color: #f8f9fa;
  color: #006633;
  border: 2rpx solid #006633;
  border-radius: 10rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.secondary-button:active {
  background-color: #e8f5e8;
  opacity: 0.9;
}

/* Send Button */
.send-button {
  border-radius: 10rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  min-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  flex: 1;
}

.send-button.enabled {
  background-color: #006633;
  color: #ffffff;
}

.send-button.enabled:active {
  background-color: #004d22;
  opacity: 0.9;
}

.send-button.disabled {
  background-color: #cccccc;
  color: #999999;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.loading-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #ffffff;
  border-top: 4rpx solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: #ffffff;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 750rpx) {
  .container {
    padding-bottom: 140rpx; /* Account for bottom nav */
  }

  .section {
    margin: 0 10rpx 20rpx 10rpx;
  }

  .form-textarea {
    height: 60rpx !important;
    min-height: 60rpx !important;
    max-height: 60rpx !important;
  }

  .pushid-display.visible {
    max-height: 180rpx;
  }

  .copy-button-container.visible {
    max-height: 80rpx;
  }

  .copy-hint {
    font-size: 20rpx;
    margin-top: 8rpx;
  }

  .button-row {
    flex-direction: column;
    gap: 15rpx;
  }

  .secondary-button,
  .send-button {
    font-size: 26rpx;
    padding: 20rpx;
    min-height: 80rpx;
  }
}

/* Font Size Adaptations */
.font-small {
  font-size: 90%;
}

.font-small .header-title {
  font-size: 32rpx;
}

.font-small .section-title {
  font-size: 28rpx;
}

.font-small .form-label {
  font-size: 24rpx;
}

.font-small .form-input,
.font-small .form-textarea {
  font-size: 24rpx;
}

.font-large {
  font-size: 110%;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .section-title {
  font-size: 36rpx;
}

.font-large .form-label {
  font-size: 32rpx;
}

.font-large .form-input,
.font-large .form-textarea {
  font-size: 32rpx;
}

/* Dark Mode Support (if needed) */
.dark-mode {
  background-color: #1a1a1a;
}

.dark-mode .header {
  background-color: #2d2d2d;
  border-bottom-color: #444444;
}

.dark-mode .section {
  background-color: #2d2d2d;
  border: 1rpx solid #444444;
}

.dark-mode .form-input,
.dark-mode .form-textarea {
  background-color: #1a1a1a;
  border-color: #444444;
  color: #ffffff;
}

.dark-mode .form-label {
  color: #ffffff;
}

.dark-mode .section-title {
  color: #ffffff;
  border-bottom-color: #444444;
}

/* Error Log Section */
.error-log-section {
  background-color: #ffffff;
  border-top: 2rpx solid #e0e0e0;
  padding: 20rpx;
  opacity: 0;
  max-height: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.error-log-section.visible {
  opacity: 1;
  max-height: 600rpx;
}

.error-log-container {
  background-color: #fff;
  border: 2rpx solid #ff4444;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.1);
}

.error-log-header {
  background-color: #ffebee;
  padding: 20rpx 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #ffcdd2;
}

.error-log-title {
  color: #c62828;
  font-size: 28rpx;
  font-weight: bold;
}

.error-log-controls {
  display: flex;
  gap: 15rpx;
}

.error-log-button {
  background-color: #c62828;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-log-button:active {
  background-color: #b71c1c;
  opacity: 0.9;
}

.error-log-text {
  background-color: #1a1a1a;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 22rpx;
  line-height: 1.4;
  padding: 20rpx;
  min-height: 300rpx;
  max-height: 500rpx;
  border: none;
  outline: none;
}

/* Responsive Design for Error Log */
@media (max-width: 750rpx) {
  .error-log-section {
    padding: 15rpx;
  }

  .error-log-text {
    font-size: 20rpx;
    padding: 15rpx;
    min-height: 250rpx;
    max-height: 400rpx;
  }

  .error-log-header {
    padding: 15rpx 20rpx;
  }

  .error-log-title {
    font-size: 26rpx;
  }

  .error-log-button {
    padding: 10rpx 16rpx;
    font-size: 22rpx;
    min-height: 56rpx;
  }
}

/* Dark Mode Support for Error Log */
.dark-mode .error-log-container {
  border-color: #ff6b6b;
  background-color: #2d2d2d;
}

.dark-mode .error-log-header {
  background-color: #3d2d2d;
  border-bottom-color: #5d3d3d;
}

.dark-mode .error-log-title {
  color: #ff6b6b;
}

.dark-mode .error-log-button {
  background-color: #d84315;
}

.dark-mode .error-log-button:active {
  background-color: #bf360c;
}