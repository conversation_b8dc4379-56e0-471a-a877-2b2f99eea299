<view class="container page-container font-{{globalFontSize}} {{liteMode ? 'lite-mode' : ''}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <!-- Normal Mode Content (unchanged) -->
  <block wx:if="{{!liteMode}}">
    <view class="header">
      <view class="back-button" bindtap="navigateBack">
        <image src="/images/back.svg" class="back-icon"></image>
      </view>
      <text class="header-title">Services</text>
    </view>
    
    <!-- Search Bar -->
    <view class="search-bar">
      <view class="search-input-container">
        <icon type="search" size="20" color="#999999"></icon>
        <input class="search-input" placeholder="Search services" value="{{searchQuery}}" bindinput="onSearchInput"></input>
      </view>
    </view>
    
    <!-- Tab Navigation -->
    <view class="tab-navigation" bindtouchstart="handleTouchStart" bindtouchmove="handleTouchMove" bindtouchend="handleTouchEnd">
      <view class="tab {{activeTab === 'theme' ? 'active' : ''}}" bindtap="switchTab" data-tab="theme">
        <text>Theme</text>
      </view>
      <view class="tab {{activeTab === 'government' ? 'active' : ''}}" bindtap="switchTab" data-tab="government">
        <text>Government & Related Organisations</text>
      </view>
      <view class="tab {{activeTab === 'commercial' ? 'active' : ''}}" bindtap="switchTab" data-tab="commercial">
        <text>Commercial & Other Organisations</text>
      </view>
    </view>
    
    <!-- Content Area with Swipe Detection -->
    <view class="content-area" 
          bindtouchstart="handleTouchStart" 
          bindtouchmove="handleTouchMove" 
          bindtouchend="handleTouchEnd">
      
      <!-- Categories Grid -->
      <view class="categories-grid" wx:if="{{activeTab === 'theme'}}">
        <view class="category-item" wx:for="{{categories}}" wx:key="index" bindtap="onCategoryTap" data-index="{{index}}">
          <image src="{{item.icon}}" class="category-icon" mode="aspectFit"></image>
          <text class="category-title">{{item.title}}</text>
        </view>
      </view>
      
      <!-- Government Tab Content -->
      <view class="government-list" wx:if="{{activeTab === 'government'}}">
        <view class="government-item" wx:for="{{governmentOrgs}}" wx:key="index" bindtap="onOrgTap" data-index="{{index}}">
          <view class="org-logo-container">
            <image src="{{item.icon || item.logo}}" class="org-logo" mode="aspectFit"></image>
          </view>
          <view class="org-info">
            <text class="org-name">{{item.name}}</text>
          </view>
          <view class="org-arrow">
            <icon type="arrow" size="16" color="#cccccc"></icon>
          </view>
        </view>
      </view>
      
      <!-- Commercial Tab Content -->
      <view class="commercial-list" wx:if="{{activeTab === 'commercial'}}">
        <block wx:for="{{commercialCategories}}" wx:key="index">
          <view class="commercial-category" bindtap="toggleCategory" data-index="{{index}}">
            <text class="category-name">{{item.name}}</text>
            <view class="category-arrow {{item.expanded ? 'expanded' : ''}}">
              <icon type="{{item.expanded ? 'up' : 'down'}}" size="16" color="#333333"></icon>
            </view>
          </view>
          
          <view class="commercial-orgs" wx:if="{{item.expanded}}">
            <view class="commercial-org-item" wx:for="{{item.organizations}}" wx:for-item="org" wx:for-index="orgIndex" wx:key="orgIndex" bindtap="onCommercialOrgTap" data-category-index="{{index}}" data-org-index="{{orgIndex}}">
              <text class="org-name">{{org.name}}</text>
              <view class="org-arrow">
                <icon type="arrow" size="16" color="#cccccc"></icon>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>

  <!-- Lite Mode Content (new) -->
  <block wx:if="{{liteMode}}">
    <view class="header">
      <text class="header-title">Services</text>
    </view>
    
    <!-- Lite Search Bar -->
    <view class="lite-search-container">
      <view class="lite-search-box">
        <image src="/images/search.svg" class="lite-search-icon"></image>
        <input placeholder="Search" class="lite-search-input" value="{{searchQuery}}" bindinput="onSearchInput"></input>
        <image src="/images/microphone.svg" class="lite-mic-icon"></image>
      </view>
    </view>
    
    <!-- Lite Tab Navigation -->
    <view class="lite-tab-navigation">
      <view class="lite-tab {{activeTab === 'theme' ? 'active' : ''}}" bindtap="switchTab" data-tab="theme">
        <text>Theme</text>
      </view>
      <view class="lite-tab {{activeTab === 'government' ? 'active' : ''}}" bindtap="switchTab" data-tab="government">
        <text>Government & Related Organisations</text>
      </view>
      <view class="lite-tab {{activeTab === 'commercial' ? 'active' : ''}}" bindtap="switchTab" data-tab="commercial">
        <text>Commercial & Other Organisations</text>
      </view>
    </view>
    
    <!-- Lite Content Area -->
    <view class="lite-content-area">
      <!-- Lite Categories Grid -->
      <view class="lite-categories-grid" wx:if="{{activeTab === 'theme'}}">
        <view class="lite-category-item" wx:for="{{categories}}" wx:key="index" bindtap="onCategoryTap" data-index="{{index}}">
          <text class="lite-category-title">{{item.title}}</text>
        </view>
      </view>
      
      <!-- Lite Government Tab Content -->
      <view class="lite-government-list" wx:if="{{activeTab === 'government'}}">
        <view class="lite-government-item" wx:for="{{governmentOrgs}}" wx:key="index" bindtap="onOrgTap" data-index="{{index}}">
          <text class="lite-org-name">{{item.name}}</text>
        </view>
      </view>
      
      <!-- Lite Commercial Tab Content -->
      <view class="lite-commercial-list" wx:if="{{activeTab === 'commercial'}}">
        <block wx:for="{{commercialCategories}}" wx:key="index">
          <block wx:for="{{item.organizations}}" wx:for-item="org" wx:for-index="orgIndex" wx:key="orgIndex">
            <view class="lite-commercial-org-item" bindtap="onCommercialOrgTap" data-category-index="{{index}}" data-org-index="{{orgIndex}}">
              <text class="lite-org-name">{{org.name}}</text>
            </view>
          </block>
        </block>
      </view>
    </view>
  </block>
  
  <!-- Direct Bottom Navigation Bar -->
  <view class="bottom-nav">
    <view class="nav-item" bindtap="navigateToHome">
      <image src="/images/home.svg" class="nav-icon"></image>
      <text class="nav-text">Home</text>
    </view>
    <view class="nav-item active" bindtap="navigateToServices">
      <image src="/images/services.svg" class="nav-icon"></image>
      <text class="nav-text">Services</text>
    </view>
    <view class="nav-item scan" bindtap="onScanTap">
      <view class="scan-circle">
        <image src="/images/scan.svg" class="scan-icon"></image>
      </view>
      <text class="nav-text">Scan</text>
    </view>
    <view class="nav-item" bindtap="navigateToMe">
      <image src="/images/me.svg" class="nav-icon"></image>
      <text class="nav-text">Me</text>
    </view>
    <view class="nav-item" bindtap="navigateToSettings">
      <image src="/images/settings.svg" class="nav-icon"></image>
      <text class="nav-text">Settings</text>
    </view>
  </view>
</view>










