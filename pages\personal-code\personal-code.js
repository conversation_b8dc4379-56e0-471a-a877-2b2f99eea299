Page({
  data: {
    codeType: 'personal',
    personalCode: {
      id: 'HKID-A1234567',
      expiry: '2028-07-14',
      status: 'Active'
    },
    healthCode: {
      status: 'Green',
      lastUpdated: '2025-07-14 16:30'
    },
    vaccinationStatus: {
      doses: 3,
      lastDose: '2024-01-15',
      certificate: true
    },
    globalFontSize: 'default'
  },
  
  onLoad: function() {
    // Initialize the page
    this.generateQRCode();
    
    // Load global font size
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize
    });
  },
  
  onShow: function() {
    // Update font size when returning to page
    const app = getApp();
    console.log('Font size from app:', app.globalData.fontSize);
    this.setData({
      globalFontSize: app.globalData.fontSize
    });
    console.log('Font size set to:', this.data.globalFontSize);
  },
  
  generateQRCode: function() {
    // Placeholder for QR code
    console.log('QR code generated');
  },
  
  switchCodeType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      codeType: type
    });
  },
  
  refreshCode: function() {
    wx.showLoading({
      title: 'Refreshing...',
    });
    
    // Simulate refresh delay
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: 'Code refreshed',
        icon: 'success'
      });
      
      // Update the last updated time for health code
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
      
      this.setData({
        'healthCode.lastUpdated': formattedDate
      });
    }, 1500);
  },
  
  showCodeInfo: function() {
    let content = '';
    
    if (this.data.codeType === 'personal') {
      content = 'Your Personal Code is a digital representation of your identity. It can be used for government services and verification purposes.';
    } else if (this.data.codeType === 'health') {
      content = 'The Health Code shows your current health status:\n\nGreen: Normal\nYellow: Caution required\nRed: Restricted movement';
    } else if (this.data.codeType === 'vaccination') {
      content = 'The Vaccination Code shows your COVID-19 vaccination status and can be used as proof of vaccination when required.';
    }
    
    wx.showModal({
      title: 'Code Information',
      content: content,
      showCancel: false,
      confirmText: 'OK'
    });
  },
  
  navigateBack: function() {
    wx.navigateBack();
  }
})



