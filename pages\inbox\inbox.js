Page({
  data: {
    activeTab: 'announcements',
    messageCount: 37,
    messages: [
      {
        title: 'Please renew your e-Cert',
        date: '12 Jul, 2025 16:15',
        unread: true
      },
      {
        title: 'You have successfully renewed your e-Cert',
        date: '11 Jul, 2025 16:17',
        unread: true
      },
      {
        title: 'Please renew your e-Cert',
        date: '10 Jul, 2025 10:40',
        unread: false
      },
      {
        title: 'You have successfully renewed your e-Cert',
        date: '09 Jul, 2025 10:41',
        unread: true
      },
      {
        title: 'Please renew your e-Cert',
        date: '29 Jun, 2025 18:55',
        unread: true
      },
      {
        title: 'You have successfully renewed your e-Cert',
        date: '29 Jun, 2025 18:51',
        unread: true
      },
      {
        title: 'Please renew your e-Cert',
        date: '18 Jun, 2025 15:00',
        unread: true
      },
      {
        title: 'You have successfully renewed your e-Cert',
        date: '18 Jun, 2025 14:57',
        unread: true
      },
      {
        title: 'Please renew your e-Cert',
        date: '17 Jun, 2025 11:30',
        unread: true
      }
    ],
    announcements: [],
    globalFontSize: 'default',
    isLoggedIn: false
  },
  
  onLoad: function() {
    // Load global font size
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onShow: function() {
    // Update font size when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  
  navigateBack: function() {
    wx.navigateBack();
  },
  
  openSettings: function() {
    console.log('Open settings');
    // Add navigation to settings page if needed
  },
  
  openMessage: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('Open message:', this.data.messages[index]);
    // Add navigation to message detail page if needed
  },
  
  openNotificationSettings: function() {
    console.log('Open notification settings');
    // Add navigation to notification settings page if needed
  },
  
  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: First we need to get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  }
})




