const notificationService = require('../services/notification.js');

Page({
  data: {
    taskId: '',
    notificationStatus: '',
    isLoading: false,
    errorMessage: ''
  },

  onLoad: function(options) {
    console.log('Check Notification page loaded');
  },

  onTaskIdInput: function(e) {
    this.setData({
      taskId: e.detail.value
    });
  },

  checkNotification: function() {
    const that = this;
    const { taskId } = this.data;

    if (!taskId) {
      wx.showToast({
        title: 'Please enter Task ID',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      errorMessage: '',
      notificationStatus: ''
    });

    // Call the notification service to check status
    notificationService.checkNotification(taskId)
      .then(response => {
        console.log('Notification check success:', response);
        that.setData({
          isLoading: false,
          notificationStatus: `Notification Status:\n${JSON.stringify(response, null, 2)}`
        });
      })
      .catch(error => {
        console.error('Notification check failed:', error);
        that.setData({
          isLoading: false,
          errorMessage: notificationService.getErrorMessage(error.code) || error.message
        });
      });
  },

  navigateBack: function() {
    wx.navigateBack();
  }
});