.container {
  padding: 60rpx 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.welcome-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.welcome-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
}

.company-section {
  margin-top: 60rpx;
}

.company-label {
  display: block;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 30rpx;
}

.company-list {
  margin-top: 40rpx;
}

.company-item {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.radio-button {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #cccccc;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-button.selected {
  border-color: #006633;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #006633;
  border-radius: 50%;
}

.company-name {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
}

.continue-button {
  margin-top: 60rpx;
  padding: 30rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.continue-button.enabled {
  background-color: #006633;
  color: white;
}

.continue-button.disabled {
  background-color: #cccccc;
  color: #666666;
}
