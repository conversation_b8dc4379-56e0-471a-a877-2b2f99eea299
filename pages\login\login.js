Page({
  data: {
    companies: [
      '---------',
      '马拉松物理治疗中心有限公司',
      '亨廷顿 Gymnastics Academy',
      'Kaboom 烟花 Ltd.'
    ],
    companyIndex: 0
  },
  
  onCompanyChange: function(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      companyIndex: index
    });
    
    // If user selects anything other than "---------"
    if (index > 0) {
      const selectedCompany = this.data.companies[index];
      
      // Store login info in global data
      const app = getApp();
      app.globalData.userInfo = {
        name: '陳大文',
        company: selectedCompany,
        isLoggedIn: true
      };
      
      // Redirect back to homepage
      wx.redirectTo({
        url: '/pages/index/index'
      });
    }
  }
})