/* Read Notification Page Styles */
.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e0e0e0;
  position: relative;
}

.back-button {
  padding: 10rpx;
  margin-right: 20rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

/* Search Bar */
.search-bar {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  opacity: 1;
  max-height: 100rpx;
  transition: all 0.3s ease;
}

.search-bar.hidden {
  opacity: 0;
  max-height: 0;
  padding: 0 30rpx;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 20rpx;
  padding: 0 30rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  outline: none;
  color: #333333;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.search-clear.visible {
  opacity: 1;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
}

/* Deep Link Data Display */
.deep-link-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  opacity: 0;
  max-height: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.deep-link-section.visible {
  opacity: 1;
  max-height: 500rpx;
}

.data-display-container {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  overflow: hidden;
}

.data-display-header {
  background-color: #006633;
  padding: 20rpx 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-display-title {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

.data-display-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.data-display-close:active {
  background-color: rgba(255, 255, 255, 0.3);
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

.data-display-text {
  background-color: #1a1a1a;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  line-height: 1.4;
  padding: 25rpx;
  min-height: 200rpx;
  max-height: 400rpx;
  border: none;
  outline: none;
}

/* Filter Tabs */
.filter-tabs {
  background-color: #ffffff;
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.filter-tab {
  flex: 1;
  background-color: transparent;
  color: #666666;
  border: none;
  border-bottom: 4rpx solid transparent;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-tab.active {
  color: #006633;
  border-bottom-color: #006633;
  background-color: rgba(0, 102, 51, 0.1);
}

.filter-tab:active {
  opacity: 0.7;
}

/* Content Area */
.content {
  flex: 1;
  padding: 0;
}

/* Notification List */
.notification-list {
  padding: 20rpx;
}

.notification-item {
  margin-bottom: 20rpx;
}

/* Notification Card */
.notification-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-card.read {
  opacity: 0.7;
  background-color: #fafafa;
}

.notification-card.unread {
  border-left: 6rpx solid #006633;
}

.notification-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* Status Indicator */
.status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.status-indicator.read {
  background-color: #cccccc;
}

.status-indicator.unread {
  background-color: #006633;
}

/* Notification Content */
.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.notification-message {
  display: flex;
  flex-direction: column;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.4;
  color: #333333;
  word-wrap: break-word;
}

/* Notification Meta */
.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.notification-time {
  font-size: 24rpx;
  color: #999999;
}

.notification-badges {
  display: flex;
  gap: 10rpx;
}

.badge {
  background-color: #f0f0f0;
  color: #666666;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  text-transform: uppercase;
  font-weight: 500;
}

.badge.priority-high {
  background-color: #ffebee;
  color: #c62828;
}

.badge.priority-normal {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.badge.priority-low {
  background-color: #e3f2fd;
  color: #1565c0;
}

/* Detail Preview */
.detail-preview {
  margin-top: 10rpx;
  padding: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #006633;
}

.detail-text {
  font-size: 26rpx;
  line-height: 1.4;
  color: #555555;
}

/* Notification Actions */
.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  align-items: center;
  justify-content: center;
  min-width: 80rpx;
}

.action-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-button.small {
  width: 48rpx;
  height: 48rpx;
}

.action-button:active {
  background-color: #f0f0f0;
  transform: scale(0.95);
}

.action-button.delete:active {
  background-color: #ffebee;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.empty-state.visible {
  opacity: 1;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.4;
  margin-bottom: 40rpx;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.loading-state.visible {
  opacity: 1;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #006633;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.loading-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

/* Primary Button (for empty state) */
.primary-button {
  background-color: #006633;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-button:active {
  background-color: #004d22;
  opacity: 0.8;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 750rpx) {
  .container {
    padding-bottom: 140rpx; /* Account for bottom nav */
  }

  .notification-list {
    padding: 15rpx;
  }

  .notification-card {
    padding: 25rpx;
  }

  .message-text {
    font-size: 28rpx;
  }
}

/* Font Size Adaptations */
.font-small {
  font-size: 90%;
}

.font-small .header-title {
  font-size: 32rpx;
}

.font-small .filter-tab {
  font-size: 24rpx;
}

.font-small .message-text {
  font-size: 26rpx;
}

.font-small .empty-title {
  font-size: 28rpx;
}

.font-large {
  font-size: 110%;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .filter-tab {
  font-size: 32rpx;
}

.font-large .message-text {
  font-size: 34rpx;
}

.font-large .empty-title {
  font-size: 36rpx;
}

/* Dark Mode Support */
.dark-mode {
  background-color: #1a1a1a;
}

.dark-mode .header {
  background-color: #2d2d2d;
  border-bottom-color: #444444;
}

.dark-mode .search-bar {
  background-color: #2d2d2d;
  border-bottom-color: #444444;
}

.dark-mode .filter-tabs {
  background-color: #2d2d2d;
  border-bottom-color: #444444;
}

.dark-mode .notification-card {
  background-color: #2d2d2d;
  border: 1rpx solid #444444;
}

.dark-mode .notification-card.read {
  background-color: #1f1f1f;
}

.dark-mode .search-input-container {
  background-color: #1a1a1a;
}

.dark-mode .search-input {
  color: #ffffff;
}

.dark-mode .message-text {
  color: #ffffff;
}

.dark-mode .notification-time {
  color: #aaaaaa;
}

.dark-mode .detail-preview {
  background-color: #1a1a1a;
  border-left-color: #006633;
}

.dark-mode .detail-text {
  color: #cccccc;
}

.dark-mode .empty-state {
  background-color: #1a1a1a;
}

.dark-mode .empty-title {
  color: #ffffff;
}

.dark-mode .empty-subtitle {
  color: #aaaaaa;
}

/* Message Display Textarea */
.message-display {
  width: 100%;
  min-height: 200rpx;  /* Double the typical default height of ~100rpx */
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #ffffff;
  font-size: 28rpx;
  line-height: 1.4;
  color: #333333;
  resize: vertical;
  box-sizing: border-box;
}

.message-display:focus {
  border-color: #006633;
  outline: none;
}

/* Message Input */
.message-input {
  width: 100%;
  height: 80rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #ffffff;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.message-input:focus {
  border-color: #006633;
  outline: none;
}

/* Retrieve Button */
.retrieve-button {
  width: 100%;
  height: 80rpx;
  background-color: #006633;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.retrieve-button:disabled {
  background-color: #cccccc;
  color: #666666;
}

.retrieve-button:not(:disabled):active {
  background-color: #004d26;
}