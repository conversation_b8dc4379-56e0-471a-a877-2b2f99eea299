Page({
  data: {
    companies: [
      '---------',
      '马拉松物理治疗中心有限公司',
      '亨廷顿 Gymnastics Academy',
      'Kaboom 烟花 Ltd.'
    ],
    companyIndex: 0,
    userName: '',
    chineseName: ''
  },
  
  onLoad: function() {
    const app = getApp();
    if (app.globalData.tempUserInfo) {
      this.setData({
        userName: app.globalData.tempUserInfo.englishName || 'User',
        chineseName: app.globalData.tempUserInfo.chineseName || ''
      });
    }
  },
  
  selectCompany: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      companyIndex: index
    });
  },
  
  continueLogin: function() {
    if (this.data.companyIndex > 0) {
      const selectedCompany = this.data.companies[this.data.companyIndex];
      const app = getApp();
      
      // Combine temp user info with company selection
      const tempUserInfo = app.globalData.tempUserInfo || {};
      
      app.globalData.userInfo = {
        isLoggedIn: true,
        name: tempUserInfo.englishName || tempUserInfo.chineseName || 'iAM Smart User',
        chineseName: tempUserInfo.chineseName || '',
        englishName: tempUserInfo.englishName || '',
        company: selectedCompany,
        accessToken: tempUserInfo.accessToken,
        openID: tempUserInfo.openID,
        gender: tempUserInfo.gender,
        birthDate: tempUserInfo.birthDate,
        idNumber: tempUserInfo.idNumber
      };
      
      // Clear temp data
      app.globalData.tempUserInfo = null;
      
      // Update global theme for logged in state
      if (app.setGlobalTheme) {
        app.setGlobalTheme(true);
      }
      
      // Redirect back to homepage
      wx.redirectTo({
        url: '/pages/index/index'
      });
    }
  }
})



