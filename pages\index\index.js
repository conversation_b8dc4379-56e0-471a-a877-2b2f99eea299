Page({
  data: {
    greeting: 'Good Afternoon',
    temperature: 'Loading...',
    weatherIcon: '/images/sunny.svg',
    alertIcons: [
      { src: '/images/alert-red.svg' },
      { src: '/images/alert-yellow.svg' }
    ],
    navItems: [
      { icon: 'search', text: 'Search' },
      { icon: 'info', text: 'Info' },
      { icon: 'inbox', text: 'Inbox' }
    ],
    newsHeadlines: [
      '醫管局通報照顧者入院',
      '比特幣升穿12萬美元創新高',
      '海關上半年共查扣侵權嫌疑貨物1.1萬批次',
      '星群的士車隊獲發正式的士車隊牌照有效期五年',
      '有內地升學機構稱近年申請來港升學內地生人數增加',
      '金管局呼籲市民提防偽冒金管局發出文件',
      '四川男子仿效電影《無雙》製偽鈔',
      '傳 Tesla 暫停機械人 Optimus 生產',
      '科學家發現防禦「永久化學物質」',
      'Tesla Robotaxi 首撞停泊車輛'
    ],
    currentNewsIndex: 0,
    activeTab: 'featured',
    featuredServices: [
      {
        title: 'eTraffic Ticket Platform (W...)',
        titleLoggedIn: '續領車輛牌照',
        icon: '/images/etraffic.svg',
        iconLoggedIn: '/images/vehicle-license-renewal.svg'
      },
      {
        title: 'Application for New Registratio...',
        titleLoggedIn: '稅務易',
        icon: '/images/application.svg',
        iconLoggedIn: '/images/etax-branded.svg'
      },
      {
        title: 'Online Voter Information Enquiry Sys...',
        titleLoggedIn: '中小企連線',
        icon: '/images/voter.svg',
        iconLoggedIn: '/images/sme-connect.svg'
      },
      {
        title: 'SmartPLAY (SIT-MO)',
        titleLoggedIn: '公司註冊處電子服務網站',
        icon: '/images/smartplay.svg',
        iconLoggedIn: '/images/companies-registry.svg'
      },
      {
        title: 'eTAX - Individual',
        titleLoggedIn: '積金易',
        icon: '/images/etax.svg',
        iconLoggedIn: '/images/empf-branded.svg'
      },
      {
        title: 'eWFSFAA',
        titleLoggedIn: '資助通',
        icon: '/images/ewfsfaa.svg',
        iconLoggedIn: '/images/funding-schemes.svg'
      },
      {
        title: 'Notification',
        titleLoggedIn: 'Notification',
        icon: '/images/notification.svg',
        iconLoggedIn: '/images/notification.svg'
      },
      {
        title: 'Check Noti',
        titleLoggedIn: 'Check Noti',
        icon: '/images/read-noti.svg',
        iconLoggedIn: '/images/read-noti.svg'
      }
    ],
    topicServices: [
      {
        icon: '/images/travelling.svg',
        title: 'Travelling',
        iconLoggedIn: '/images/trade-business.svg',
        titleLoggedIn: '貿易及營商'
      },
      {
        icon: '/images/newborn.svg',
        title: 'Newborn Parent',
        iconLoggedIn: '/images/taxation.svg',
        titleLoggedIn: '稅務'
      },
      {
        icon: '/images/kindergarten.svg',
        title: 'Kindergarten Admission',
        iconLoggedIn: '/images/company-registration.svg',
        titleLoggedIn: '註冊及牌照'
      },
      {
        icon: '/images/elderly.svg',
        title: 'Info for Elderly',
        iconLoggedIn: '/images/market-data.svg',
        titleLoggedIn: '市場資訊及統計數據'
      }
    ],
    harbourInfo: [
      {
        name: 'Western Harbour Crossing',
        location: 'Kowloon',
        speeds: [
          { value: '80', unit: 'km/h' },
          { value: '72', unit: 'km/h' }
        ],
        destination: 'Hong Kong',
        fees: { car: '$30', truck: '$12' }
      },
      {
        name: 'Cross Harbour Tunnel',
        location: 'Kowloon',
        speeds: [
          { value: '44', unit: 'km/h' },
          { value: '47', unit: 'km/h' }
        ],
        destination: 'Hong Kong',
        fees: { car: '$30', truck: '$12' }
      },
      {
        name: 'Eastern Harbour Crossing',
        location: 'Kowloon',
        speeds: [
          { value: '60', unit: 'km/h' },
          { value: '57', unit: 'km/h' }
        ],
        destination: 'Hong Kong',
        fees: { car: '$30', truck: '$12' }
      }
    ],
    updateTime: '12:20',
    globalFontSize: 'default',
    liteMode: false,
    inboxCount: 1,
    recentServices: [
      { title: 'CorpID Mini-program' },
      { title: 'Online Voter Information Enquiry Sys...' },
      { title: 'DAA141 In-App Browser Demo (PoC)' },
      { title: 'Civil Aid Service Cadet Corps' }
    ],
    isLoggedIn: false
  },
  onLoad: function () {
    this.setGreetingByTime();
    this.fetchWeatherData();
    this.startNewsRotation();
    this.updateTime();

    // Load global font size and lite mode
    const app = getApp();
    const isLoggedIn = app.globalData.userInfo && app.globalData.userInfo.isLoggedIn;
    console.log("onLoad - isLoggedIn:", isLoggedIn);
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: isLoggedIn
    });

    // Initialize service icons based on login status
    this.updateServiceIcons(isLoggedIn);

    // Auto-login if user is not logged in - DISABLED
    // if (!isLoggedIn) {
    //   console.log("User not logged in, starting auto-login...");
    //   // Add a small delay to ensure page is fully loaded
    //   setTimeout(() => {
    //     this.startAutoLogin();
    //   }, 1000);
    // }
  },
  onShow: function (options) {
    console.log("onShow called with options:", options);
    this.checkLoginStatus();

    // Update font size and lite mode when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });

    // Check if returning from iAMSmart with authCode
    if (options && options.authCode) {
      console.log("Received authCode from iAMSmart:", options.authCode);
      this.handleAuthCode(options.authCode);
    } else if (options && options.action === 'reauth') {
      console.log("Returning from re-authentication");
      wx.hideLoading();
      
      // Navigate to Me page after successful re-authentication
      wx.navigateTo({
        url: '/pages/me/me'
      });
    } else {
      console.log("No authCode or reauth action found in options");
    }
  },
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  navigateToMore: function () {
    if (this.data.activeTab === 'featured') {
      wx.navigateTo({
        url: '/pages/featured-services/featured-services'
      });
    } else if (this.data.activeTab === 'topics') {
      wx.navigateTo({
        url: '/pages/topics/topics'
      });
    }
  },
  setGreetingByTime: function () {
    const hour = new Date().getHours();
    let greeting = 'Good Morning';

    if (hour >= 12 && hour < 18) {
      greeting = 'Good Afternoon';
    } else if (hour >= 18) {
      greeting = 'Good Evening';
    }

    this.setData({ greeting });
  },
  updateTime: function () {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    this.setData({
      updateTime: `${hours}:${minutes}`
    });
  },
  fetchWeatherData: function () {
    // Hardcode the temperature
    this.setData({
      temperature: '32°C',
      weatherIcon: '/images/sunny.svg'
    });
  },
  startNewsRotation: function () {
    // Clear any existing interval
    if (this.newsInterval) {
      clearInterval(this.newsInterval);
    }

    // Start smooth scrolling animation
    this.newsInterval = setInterval(() => {
      let nextIndex = this.data.currentNewsIndex + 1;
      if (nextIndex >= this.data.newsHeadlines.length) {
        nextIndex = 0;
      }

      this.setData({
        currentNewsIndex: nextIndex
      });
    }, 3000);
  },
  onUnload: function () {
    // Clear the interval when page is unloaded
    if (this.newsInterval) {
      clearInterval(this.newsInterval);
    }
  },
  onNavItemTap: function (e) {
    const index = e.currentTarget.dataset.index;
    const navItem = this.data.navItems[index];

    if (navItem.text === 'Search') {
      wx.navigateTo({
        url: '/pages/search/search'
      });
    } else if (navItem.text === 'Info') {
      wx.navigateTo({
        url: '/pages/info/info'
      });
    } else if (navItem.text === 'Inbox') {
      wx.navigateTo({
        url: '/pages/inbox/inbox'
      });
    }
  },
  // AUTO-LOGIN FUNCTION - DISABLED
  // startAutoLogin: function () {
  //   console.log("=== STARTING AUTO-LOGIN ===");

  //   // loading indicator
  //   wx.showLoading({
  //     title: 'Initializing...',
  //     mask: false
  //   });

  //   // Use the same authentication logic as manual login
  //   const authParams = {
  //     clientID: "bcff81d61e1b469b8eb6f6dd034e2305",
  //     lang:"en-US",
  //     redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q",
  //     responseType: "code",
  //     scope: "eidapi_auth",
  //     source: "Mini_Program",
  //     state: "123456",

  //     success: (res) => {
  //       console.log("=== AUTO-LOGIN SUCCESS ===");
  //       console.log("Auto-login success callback triggered with:", res);

  //       wx.hideLoading();

  //       // Look for the authorization code
  //       let authCode = null;
  //       if (res.content && res.content.code) {
  //         authCode = res.content.code;
  //         console.log("AuthCode found in content.code:", authCode);
  //       } else if (res.authCode) {
  //         authCode = res.authCode;
  //         console.log("AuthCode found in authCode:", authCode);
  //       } else if (res.code && res.code !== "M00000") {
  //         authCode = res.code;
  //         console.log("AuthCode found in code:", authCode);
  //       }

  //       if (authCode) {
  //         console.log("Auto-login using authCode:", authCode);
  //         this.handleAuthCode(authCode);
  //       } else {
  //         console.log("No valid authCode found in auto-login response");
  //       }
  //       console.log("=== END AUTO-LOGIN SUCCESS ===");
  //     },
  //     fail: (err) => {
  //       console.log("=== AUTO-LOGIN FAILED ===");
  //       console.error("Auto-login failed with error:", err);

  //       wx.hideLoading();

  //       // Just log the error and let user manually login if needed
  //       console.log("Auto-login failed, user can manually login if needed");
  //       console.log("=== END AUTO-LOGIN FAIL ===");
  //     }
  //   };

  //   console.log("Starting auto-login authentication...");

  //   try {
  //     wx.authentication(authParams);
  //   } catch (error) {
  //     console.error("Auto-login wx.authentication call failed:", error);
  //     wx.hideLoading();
  //   }
  // },

  onLoginLogoutTap: function () {
    console.log("Login/Logout button tapped");

    if (this.data.userInfo && this.data.userInfo.isLoggedIn) {
      console.log("User is logged in, performing logout");
      // Logout
      const app = getApp();
      app.globalData.userInfo = {
        isLoggedIn: false,
        name: '',
        company: ''
      };
      this.setData({
        userInfo: {
          isLoggedIn: false,
          name: '',
          company: ''
        },
        isLoggedIn: false
      });

      // Update global theme
      if (app.setGlobalTheme) {
        app.setGlobalTheme(false);
      }

      // Restore original service icons
      this.updateServiceIcons(false);

      console.log("Logout completed");
    } else {
      console.log("User not logged in, starting iAMSmart authentication");
      
      // 1. Define the parameters for the authentication call
      const authParams = {
        clientID: "bcff81d61e1b469b8eb6f6dd034e2305",
        lang:"en-US",
        redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q",
        responseType: "code",
        scope: "eidapi_auth",
        source: "Mini_Program",
        state: "123456",

        success: (res) => {
          console.log("=== SUCCESS CALLBACK TRIGGERED ===");
          console.log("wx.authentication success callback triggered with:", res);
          console.log("Full response object:", JSON.stringify(res, null, 2));
          
          // Look for the authorization code in the content object first
          let authCode = null;
          if (res.content && res.content.code) {
            authCode = res.content.code;
            console.log("AuthCode found in content.code:", authCode);
          } else if (res.authCode) {
            authCode = res.authCode;
            console.log("AuthCode found in authCode:", authCode);
          } else if (res.code && res.code !== "M00000") {
            authCode = res.code;
            console.log("AuthCode found in code:", authCode);
          }
          
          if (authCode) {
            console.log("Using authCode:", authCode);
            this.handleAuthCode(authCode);
          } else {
            console.log("No valid authCode found in success callback response");
          }
          console.log("=== END SUCCESS CALLBACK ===");
        },
        fail: (err) => {
          console.log("=== FAIL CALLBACK TRIGGERED ===");
          console.error("wx.authentication failed with error:", err);
          console.error("Error details:", JSON.stringify(err));
          
          if (err.errMsg && err.errMsg.includes('Invalid Online Service URL')) {
            console.error("redirectURI issue - check if URL is registered correctly");
            wx.showModal({
              title: 'Configuration Error',
              content: 'The redirect URL is not properly configured. Please contact support.',
              showCancel: false
            });
          } else {
            wx.showToast({ 
              title: 'Authorization Failed: ' + (err.errMsg || 'Unknown error'), 
              icon: 'none',
              duration: 3000
            });
          }
          console.log("=== END FAIL CALLBACK ===");
        }
      };

      console.log("Calling wx.authentication with params:", authParams);
      console.log("redirectURI being used:", authParams.redirectURI);
      
      // 2. Call the authentication API
      try {
        console.log("About to call wx.authentication...");
        wx.authentication(authParams);
        console.log("wx.authentication call completed - waiting for callbacks");
        
        // Add a timeout to check if callbacks are called
        setTimeout(() => {
          console.log("5 seconds passed since wx.authentication call - checking if callbacks were triggered");
        }, 5000);
        
      } catch (error) {
        console.error("Exception when calling wx.authentication:", error);
        wx.showToast({
          title: 'Authentication API Error',
          icon: 'none'
        });
      }
    }
  },
  updateServiceIcons: function (isLoggedIn) {
    console.log("=== UPDATING SERVICE ICONS ===");
    console.log("Login status:", isLoggedIn);
    console.log("Current featuredServices data:", this.data.featuredServices);
    console.log("Current topicServices data:", this.data.topicServices);

    // Update Featured Services
    const updatedServices = this.data.featuredServices.map((service, index) => {
      const currentIcon = isLoggedIn ? service.iconLoggedIn : service.icon;
      const currentTitle = isLoggedIn ? service.titleLoggedIn : service.title;
      console.log(`Featured Service ${index}:`);
      console.log(`  Original title: ${service.title}`);
      console.log(`  Logged-in title: ${service.titleLoggedIn}`);
      console.log(`  Current title: ${currentTitle}`);
      console.log(`  Original icon: ${service.icon}`);
      console.log(`  Logged-in icon: ${service.iconLoggedIn}`);
      console.log(`  Current icon: ${currentIcon}`);

      return {
        ...service,
        currentIcon: currentIcon,
        currentTitle: currentTitle
      };
    });

    // Update Topic Services
    const updatedTopics = this.data.topicServices.map((topic, index) => {
      const currentIcon = isLoggedIn ? topic.iconLoggedIn : topic.icon;
      const currentTitle = isLoggedIn ? topic.titleLoggedIn : topic.title;
      console.log(`Topic Service ${index}:`);
      console.log(`  Original title: ${topic.title}`);
      console.log(`  Logged-in title: ${topic.titleLoggedIn}`);
      console.log(`  Current title: ${currentTitle}`);
      console.log(`  Original icon: ${topic.icon}`);
      console.log(`  Logged-in icon: ${topic.iconLoggedIn}`);
      console.log(`  Current icon: ${currentIcon}`);

      return {
        ...topic,
        currentIcon: currentIcon,
        currentTitle: currentTitle
      };
    });

    console.log("Final updated services:", updatedServices);
    console.log("Final updated topics:", updatedTopics);

    this.setData({
      featuredServices: updatedServices,
      topicServices: updatedTopics
    });

    console.log("setData completed for both services and topics");
  },
  checkLoginStatus: function () {
    console.log("checkLoginStatus called");
    const app = getApp();
    console.log("Current app.globalData.userInfo:", app.globalData.userInfo);

    if (app.globalData.userInfo && app.globalData.userInfo.isLoggedIn) {
      console.log("User is logged in, updating page data");
      this.setData({
        userInfo: app.globalData.userInfo
      });
      this.updateServiceIcons(true);
    } else {
      console.log("User not logged in, setting default state");
      this.setData({
        userInfo: {
          isLoggedIn: false,
          name: '',
          company: ''
        }
      });
      this.updateServiceIcons(false);
    }
  },
  onScanTap: function () {
    // Disabled because scanner always crash
    wx.showToast({
      title: 'Scan feature temporarily unavailable',
      icon: 'none'
    });

    /*
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('QR Code scan result:', res);
        // Handle QR code specific logic
        this.handleQRCodeResult(res.result);
      },
      fail: (err) => {
        console.log('QR scan failed:', err);
        wx.showToast({
          title: 'QR scan cancelled',
          icon: 'none'
        });
      }
    });
    */
  },

  /*
  handleQRCodeResult: function(result) {
    // Miniapp specific QR code handling
    if (result.includes('corpid') || result.includes('personal-code')) {
      // Navigate to personal code verification
      wx.navigateTo({
        url: '/pages/personal-code/personal-code?code=' + encodeURIComponent(result)
      });
    } else {
      // Show generic result
      wx.showModal({
        title: 'QR Code Scanned',
        content: result,
        showCancel: false
      });
    }
  },
  */
  onServiceTap: function (e) {
    const index = e.currentTarget.dataset.index;
    const service = this.data.featuredServices[index];

    if (service.currentTitle.includes('eWFSFAA')) {
       wx.navigateTo({
         url: '/pages/test-navigation/test-navigation'
       });
     } else if (service.currentTitle.includes('Notification')) {
       wx.navigateTo({
         url: '/pages/notification/write_noti'
       });
     } else if (service.currentTitle.includes('Check Noti')) {
       wx.navigateTo({
         url: '/pages/notification/check_noti'
       });
     } else {
       console.log('Service tapped:', service.currentTitle);
     }
  },
  // Bottom navigation handlers
  navigateToHome: function () {
    // Already on home page, do nothing or refresh
    console.log('Already on home page');
  },

  navigateToSearch: function () {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  navigateToMe: function () {
    // Check if user is logged in first
    if (!this.data.userInfo || !this.data.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: First we need to get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        // Include any required parameters for ticket request
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          // Use the received ticketID for re-authentication
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    // Step 2: Use the received ticketID for re-authentication
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        // Navigate to Me page after successful re-authentication
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      // Call the re-authentication API with the proper ticketID
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  },

  navigateToSettings: function () {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },
  navigateToServices: function () {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },
  handleAuthCode: function(authCode) {
    console.log("handleAuthCode called with authCode:", authCode);
    
    // Loading indicator
    wx.showLoading({
      title: 'Authenticating...',
    });

    const requestData = {
      authCode: authCode,
      clientId: "bcff81d61e1b469b8eb6f6dd034e2305",
      state: "123456"
    };
    
    console.log("Sending POST request to backend with data:", requestData);

    // Send authCode to SpringBoot backend
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/iamsmart-login',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log("=== BACKEND REQUEST SUCCESS ===");
        console.log("Status Code:", res.statusCode);
        console.log("Response Headers:", res.header);
        console.log("Response Data:", JSON.stringify(res.data, null, 2));
        console.log("=== END BACKEND RESPONSE ===");
        
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data) {
          console.log("Authentication successful, checking response data");
          
          const responseData = res.data;
          
          // Check if there's a profile error
          if (responseData.profileError) {
            console.log("Profile error received:", responseData.profileError);
            
            wx.hideLoading();
            wx.showModal({
              title: 'Profile Error',
              content: 'Failed to retrieve user profile: ' + (responseData.profileError.message || 'Unknown error'),
              showCancel: false,
              confirmText: 'OK'
            });
            return;
          }
          
          // Check if profile was successful
          if (responseData.profileSuccess && responseData.openID) {
            console.log("Profile retrieved successfully, navigating to company selection");
            
            const app = getApp();
            
            // Store the authenticated user data temporarily
            app.globalData.tempUserInfo = {
              chineseName: responseData.chineseName || '',
              englishName: responseData.englishName || '',
              accessToken: responseData.accessToken,
              openID: responseData.openID,
              gender: responseData.gender,
              birthDate: responseData.birthDate,
              idNumber: responseData.idNumber
            };
            
            wx.hideLoading();
            
            // Navigate to company selection page
            wx.navigateTo({
              url: '/pages/company-selection/company-selection'
            });
            
          } else {
            console.log("Profile retrieval failed or incomplete data");
            wx.showToast({
              title: 'Profile retrieval failed',
              icon: 'none'
            });
          }
        } else {
          console.log("Authentication failed - Status:", res.statusCode, "Data:", res.data);
          wx.showToast({
            title: 'Authentication Failed',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.log("=== BACKEND REQUEST FAILED ===");
        console.error("Error Message:", err.errMsg);
        console.error("Error Code:", err.errno);
        console.error("Full Error Object:", JSON.stringify(err, null, 2));
        console.log("=== END BACKEND ERROR ===");
        
        wx.hideLoading();
        wx.showToast({
          title: 'Network Error: ' + (err.errMsg || 'Unknown'),
          icon: 'none',
          duration: 3000
        });
      },
      complete: (res) => {
        console.log("=== REQUEST COMPLETED ===");
        console.log("Request to backend completed, status:", res ? res.statusCode : 'No response');
      }
    });
  }
})














































