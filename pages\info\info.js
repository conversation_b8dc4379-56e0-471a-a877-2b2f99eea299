Page({
  data: {
    infoItems: [
      { 
        title: 'A&E Waiting Time',
        icon: '/images/hospital.svg'
      },
      { 
        title: 'AED',
        icon: '/images/aed.svg'
      },
      { 
        title: 'Map of Recycling Points',
        icon: '/images/recycling.svg'
      },
      { 
        title: 'Traffic',
        icon: '/images/traffic.svg'
      },
      { 
        title: 'HKeMobility',
        icon: '/images/mobility.svg'
      },
      { 
        title: '1823 Enquiry Service',
        icon: '/images/enquiry.svg'
      },
      { 
        title: 'Weather',
        icon: '/images/weather.svg'
      },
      { 
        title: 'Environment',
        icon: '/images/environment.svg'
      },
      { 
        title: 'Public Facilities',
        icon: '/images/facilities.svg'
      },
      { 
        title: 'Scameter',
        icon: '/images/scameter.svg'
      },
      { 
        title: 'Jobs',
        icon: '/images/jobs.svg'
      },
      { 
        title: 'Government Information',
        icon: '/images/government.svg'
      },
      { 
        title: 'Cross-boundary Information',
        icon: '/images/cross-boundary.svg'
      }
    ],
    globalFontSize: 'default',
    isLoggedIn: false
  },
  
  onLoad: function() {
    // Load global font size
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onShow: function() {
    // Update font size when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  navigateBack: function() {
    wx.navigateBack();
  },
  
  onItemTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.infoItems[index];
    console.log('Tapped on:', item.title);
    // Add navigation logic here if needed
  },

  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: Get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  },
})



