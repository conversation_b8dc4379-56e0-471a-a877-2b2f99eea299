<view class="container page-container font-{{globalFontSize}} {{liteMode ? 'lite-mode' : ''}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Settings</text>
  </view>
  
  <!-- App Settings Section -->
  <view class="settings-section">
    <view class="section-title">App Settings</view>
    
    <!-- Lite Mode Toggle -->
    <view class="settings-item toggle-item">
      <view class="item-icon">
        <image src="/images/layers.svg" class="setting-icon"></image>
      </view>
      <view class="item-title">Switch to Lite Mode</view>
      <switch checked="{{liteMode}}" bindchange="toggleLiteModeItem" color="#006633" class="lite-switch"></switch>
    </view>
    
    <!-- Settings List -->
    <view class="settings-list">
      <block wx:for="{{settingsItems}}" wx:key="index">
        <view class="list-item" bindtap="navigateToSettingDetail" data-index="{{index}}">
          <view class="item-icon">
            <image src="/images/{{item.icon}}.svg" class="setting-icon"></image>
          </view>
          <view class="item-title">{{item.title}}</view>
          <view class="item-arrow" wx:if="{{item.arrow !== false}}">
            <image src="/images/arrow-right.svg" class="arrow-icon"></image>
          </view>
          <view class="item-version" wx:if="{{item.version}}">
            <text class="version-text">{{item.version}}</text>
          </view>
        </view>
      </block>
    </view>
  </view>
</view>

<!-- Direct Bottom Navigation Bar -->
<view class="bottom-nav">
  <view class="nav-item" bindtap="navigateToHome">
    <image src="/images/home.svg" class="nav-icon"></image>
    <text class="nav-text">Home</text>
  </view>
  <view class="nav-item" bindtap="navigateToServices">
    <image src="/images/services.svg" class="nav-icon"></image>
    <text class="nav-text">Services</text>
  </view>
  <view class="nav-item scan" bindtap="onScanTap">
    <view class="scan-circle">
      <image src="/images/scan.svg" class="scan-icon"></image>
    </view>
    <text class="nav-text">Scan</text>
  </view>
  <view class="nav-item" bindtap="navigateToMe">
    <image src="/images/me.svg" class="nav-icon"></image>
    <text class="nav-text">Me</text>
  </view>
  <view class="nav-item active" bindtap="navigateToSettings">
    <image src="/images/settings.svg" class="nav-icon"></image>
    <text class="nav-text">Settings</text>
  </view>
</view>


















