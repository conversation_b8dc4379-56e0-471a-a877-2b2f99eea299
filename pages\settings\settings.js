Page({
  data: {
    userName: 'Unknown User',
    liteMode: false,
    allSettingsItems: [
      {
        icon: 'display',
        title: 'Display',
        arrow: true,
        path: '/pages/settings/display/display'
      },
      {
        icon: 'qrcode',
        title: 'Personal Code',
        arrow: true,
        path: '/pages/settings/personal-code/personal-code'
      },
      {
        icon: 'storage',
        title: 'Storage',
        arrow: true,
        path: '/pages/settings/storage/storage'
      },
      {
        icon: 'guide',
        title: 'User Guide',
        arrow: true,
        path: '/pages/settings/user-guide/user-guide'
      },
      {
        icon: 'help',
        title: 'Help',
        arrow: true,
        path: '/pages/settings/help/help'
      },
      {
        icon: 'about',
        title: 'About CorpID',
        arrow: true,
        path: '/pages/settings/about/about'
      },
      {
        icon: 'terms',
        title: 'Terms of Service',
        arrow: true,
        path: '/pages/settings/terms/terms'
      },
      {
        icon: 'privacy',
        title: 'Privacy Policy',
        arrow: true,
        path: '/pages/settings/privacy/privacy'
      },
      {
        icon: 'feedback',
        title: 'Feedback',
        arrow: true,
        path: '/pages/settings/feedback/feedback'
      }
    ],
    settingsItems: [],
    globalFontSize: 'default',
    isLoggedIn: false
  },

  updateSettingsItems: function() {
    const liteItems = [
      this.data.allSettingsItems[0], // Display
      this.data.allSettingsItems[1], // Personal Code  
      this.data.allSettingsItems[2], // Storage
      this.data.allSettingsItems[4], // Help
      this.data.allSettingsItems[5], // About CorpID
      this.data.allSettingsItems[6], // Terms of Service
      {
        icon: 'build',
        title: 'Build',
        version: '4.4.0',
        arrow: false
      }
    ];
    
    this.setData({
      settingsItems: this.data.liteMode ? liteItems : this.data.allSettingsItems
    });
  },
  
  onLoad: function() {
    // Load user info
    const app = getApp();
    const userInfo = app.globalData.userInfo;
    const userName = userInfo && userInfo.isLoggedIn ? userInfo.name : 'Unknown User';
    
    // Load global font size and lite mode
    const liteMode = app.globalData.liteMode;
    const fontSize = liteMode ? 'large' : app.globalData.fontSize;
    
    this.setData({
      userName: userName,
      globalFontSize: fontSize,
      liteMode: typeof liteMode === 'boolean' ? liteMode : false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
    this.updateSettingsItems();
  },
  
  onShow: function() {
    // Update user info when returning to page
    const app = getApp();
    const userInfo = app.globalData.userInfo;
    const userName = userInfo && userInfo.isLoggedIn ? userInfo.name : 'Unknown User';
    
    // Update font size and lite mode when returning to page
    this.setData({
      userName: userName,
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
    this.updateSettingsItems();
  },
  
  toggleLiteModeItem: function(e) {
    const newValue = e.detail.value;
    console.log('Toggle value:', newValue, typeof newValue);
    
    // Set font size to large when enabling lite mode, default when disabling
    const newFontSize = newValue ? 'large' : 'default';
    
    this.setData({
      liteMode: newValue,
      globalFontSize: newFontSize
    });
    
    // Update settings items based on lite mode
    this.updateSettingsItems();
    
    // Apply globally through app instance
    const app = getApp();
    if (app.setGlobalLiteMode) {
      app.setGlobalLiteMode(newValue);
    }
    if (app.setGlobalFontSize) {
      app.setGlobalFontSize(newFontSize);
    }
    
    // Force a re-render by updating the page data again
    setTimeout(() => {
      this.setData({
        globalFontSize: newFontSize,
        liteMode: newValue
      });
    }, 100);
    
    console.log('Lite Mode:', newValue, 'Font Size:', newFontSize);
  },
  
  navigateToSettingDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.settingsItems[index];
    
    if (item.path) {
      wx.navigateTo({
        url: item.path
      });
    }
  },
  
  navigateToProfile: function() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },
  onLogoutTap: function() {
    // Logout functionality - same as homepage
    const app = getApp();
    app.globalData.userInfo = {
      isLoggedIn: false,
      name: '',
      company: ''
    };
    this.setData({
      userName: 'Unknown User'
    });
  },
  navigateToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },
  navigateBack: function() {
    wx.navigateBack();
  },
  // Bottom navigation handlers
  navigateToHome: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },
  
  navigateToServices: function() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },
  
  onScanTap: function() {
    // Temporarily disabled due to scanner crashes
    wx.showToast({
      title: 'Scan feature temporarily unavailable',
      icon: 'none'
    });
    
    /*
    wx.scanCode({
      success: (res) => {
        console.log('Scan result:', res);
        wx.showModal({
          title: 'Scan Result',
          content: res.result,
          showCancel: false
        });
      },
      fail: (err) => {
        console.log('Scan failed:', err);
        wx.showToast({
          title: 'Scan cancelled',
          icon: 'none'
        });
      }
    });
    */
  },
  
  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: Get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  },
  
  navigateToSettings: function() {
    // Already on settings page
    console.log('Already on settings page');
  }
})































