Page({
  data: {
    // Default services (when not logged in)
    defaultServices: [
      {
        title: 'eTraffic Ticket Platform (Website)',
        icon: '/images/etraffic.svg',
        favorite: false
      },
      {
        title: 'Application for New Registration / Report on Change of Particulars by an Elector in a Geographical Constituency (REO-GC)',
        icon: '/images/application.svg',
        favorite: false
      },
      {
        title: 'Online Voter Information Enquiry System',
        icon: '/images/voter.svg',
        favorite: false
      },
      {
        title: 'SmartPLAY (SIT-MO)',
        icon: '/images/smartplay.svg',
        favorite: false
      },
      {
        title: 'Notification',
        icon: '/images/notification.svg',
        favorite: false
      },
      {
        title: 'eWFSFAA',
        icon: '/images/ewfsfaa.svg',
        favorite: false
      },
      {
        title: 'eTAX - Individual Tax Portal (ITP-UAT 2)',
        icon: '/images/etax.svg',
        favorite: false
      },
      {
        title: 'Renew Vehicle Licence',
        icon: '/images/vehicle.svg',
        favorite: false
      },
      {
        title: 'International Driving Permit',
        icon: '/images/driving.svg',
        favorite: false
      },
      {
        title: 'Contactless e-Channel Clearance',
        icon: '/images/echannel.svg',
        favorite: false
      },
      {
        title: 'HA Go',
        icon: '/images/hago.svg',
        favorite: false
      },
      {
        title: 'ELicensing Portal',
        icon: '/images/elicensing.svg',
        favorite: false
      }
    ],
    // Services when logged in (using the correct icon paths we created)
    loggedInServices: [
      {
        title: '續領車輛牌照',
        icon: '/images/vehicle-license-renewal.svg',
        favorite: false
      },
      {
        title: '稅務易',
        icon: '/images/etax-branded.svg',
        favorite: false
      },
      {
        title: '中小企連線',
        icon: '/images/sme-connect.svg',
        favorite: false
      },
      {
        title: '公司註冊處電子服務網站',
        icon: '/images/companies-registry.svg',
        favorite: false
      },
      {
        title: '積金易',
        icon: '/images/empf-branded.svg',
        favorite: false
      },
      {
        title: '資助通',
        icon: '/images/funding-schemes.svg',
        favorite: false
      },
      {
        title: '租用郵政私用信箱',
        icon: '/images/hongkong-post.svg',
        favorite: false
      },
      {
        title: '申請營物業牌照',
        icon: '/images/property-license.svg',
        favorite: false
      }
    ],
    services: [],
    globalFontSize: 'default',
    isLoggedIn: false
  },
  
  onLoad: function() {
    console.log('=== FEATURED SERVICES onLoad ===');
    // Load global font size and check login status (copied from index.js)
    const app = getApp();
    console.log('app.globalData.userInfo:', app.globalData.userInfo);
    const isLoggedIn = app.globalData.userInfo && app.globalData.userInfo.isLoggedIn;
    console.log('isLoggedIn:', isLoggedIn);
    console.log('defaultServices length:', this.data.defaultServices.length);
    console.log('loggedInServices length:', this.data.loggedInServices.length);

    const servicesToUse = isLoggedIn ? this.data.loggedInServices : this.data.defaultServices;
    console.log('Using services:', servicesToUse.length, 'items');
    console.log('First service title:', servicesToUse[0]?.title);

    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: isLoggedIn,
      services: servicesToUse
    });
    console.log('=== END FEATURED SERVICES onLoad ===');
  },

  onShow: function() {
    console.log('=== FEATURED SERVICES onShow ===');
    // Update font size and check login status when returning to page (copied from index.js)
    const app = getApp();
    console.log('app.globalData.userInfo:', app.globalData.userInfo);
    const isLoggedIn = app.globalData.userInfo && app.globalData.userInfo.isLoggedIn;
    console.log('isLoggedIn:', isLoggedIn);

    const servicesToUse = isLoggedIn ? this.data.loggedInServices : this.data.defaultServices;
    console.log('Using services:', servicesToUse.length, 'items');
    console.log('First service title:', servicesToUse[0]?.title);

    this.setData({
      globalFontSize: app.globalData.fontSize,
      isLoggedIn: isLoggedIn,
      services: servicesToUse
    });
    console.log('=== END FEATURED SERVICES onShow ===');
  },
  
  toggleFavorite: function(e) {
    const index = e.currentTarget.dataset.index;
    const favoriteKey = `services[${index}].favorite`;
    const newValue = !this.data.services[index].favorite;

    this.setData({
      [favoriteKey]: newValue
    });
  },

  navigateBack: function() {
    wx.navigateBack();
  },

  // Handle service item taps
  onServiceTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const service = this.data.services[index];
    console.log('Featured service tapped:', service.title);

    // Handle notification service specifically
    if (service.title === 'Notification') {
      wx.navigateTo({
        url: '/pages/notification/write_noti'
      });
      return;
    }

    // Handle other services (placeholder for future implementation)
    console.log('Other service tapped - to be implemented');
  }
})
