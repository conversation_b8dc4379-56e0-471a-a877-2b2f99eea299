<view class="container page-container font-{{globalFontSize}}">
  <!-- Header -->
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <view class="title">Personal Code</view>
    <view class="info-button" bindtap="showCodeInfo">
      <image src="/images/help.svg" class="info-icon"></image>
    </view>
  </view>
  
  <!-- Code Type Selector -->
  <view class="code-selector">
    <view class="selector-item {{codeType === 'personal' ? 'active' : ''}}" bindtap="switchCodeType" data-type="personal">
      <text>Personal</text>
    </view>
    <view class="selector-item {{codeType === 'health' ? 'active' : ''}}" bindtap="switchCodeType" data-type="health">
      <text>Health</text>
    </view>
    <view class="selector-item {{codeType === 'vaccination' ? 'active' : ''}}" bindtap="switchCodeType" data-type="vaccination">
      <text>Vaccination</text>
    </view>
  </view>
  
  <!-- QR Code Display -->
  <view class="code-display">
    <!-- Personal Code -->
    <block wx:if="{{codeType === 'personal'}}">
      <view class="code-container">
        <image src="/images/personal-code-icon.png" class="qr-code" mode="aspectFit"></image>
        <view class="code-info">
          <view class="info-row">
            <text class="info-label">ID:</text>
            <text class="info-value">{{personalCode.id}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">Expiry:</text>
            <text class="info-value">{{personalCode.expiry}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">Status:</text>
            <text class="info-value status-active">{{personalCode.status}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- Health Code -->
    <block wx:elif="{{codeType === 'health'}}">
      <view class="code-container">
        <view class="health-code {{healthCode.status.toLowerCase()}}">
          <text class="health-status">{{healthCode.status}}</text>
        </view>
        <view class="code-info">
          <view class="info-row">
            <text class="info-label">Status:</text>
            <text class="info-value status-{{healthCode.status.toLowerCase()}}">{{healthCode.status}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">Updated:</text>
            <text class="info-value">{{healthCode.lastUpdated}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- Vaccination Code -->
    <block wx:elif="{{codeType === 'vaccination'}}">
      <view class="code-container">
        <image src="/images/personal-code-icon.png" class="qr-code" mode="aspectFit"></image>
        <view class="code-info">
          <view class="info-row">
            <text class="info-label">Doses:</text>
            <text class="info-value">{{vaccinationStatus.doses}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">Last Dose:</text>
            <text class="info-value">{{vaccinationStatus.lastDose}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">Certificate:</text>
            <text class="info-value status-active">{{vaccinationStatus.certificate ? 'Available' : 'Not Available'}}</text>
          </view>
        </view>
      </view>
    </block>
  </view>
  
  <!-- Action Buttons -->
  <view class="action-buttons">
    <view class="action-button refresh" bindtap="refreshCode">
      <image src="/images/refresh.svg" class="button-icon"></image>
      <text>Refresh</text>
    </view>
    <view class="action-button share">
      <image src="/images/share.svg" class="button-icon"></image>
      <text>Share</text>
    </view>
    <view class="action-button download">
      <image src="/images/download.svg" class="button-icon"></image>
      <text>Download</text>
    </view>
  </view>
  
  <!-- Usage Instructions -->
  <view class="instructions">
    <view class="instructions-title">How to use</view>
    <view class="instructions-content">
      <text>1. Present this code when requested by authorized personnel</text>
      <text>2. Ensure your screen brightness is set to maximum</text>
      <text>3. Keep your code up to date by refreshing regularly</text>
    </view>
  </view>
  
  <!-- Verification Notice -->
  <view class="verification-notice">
    <image src="/images/privacy.svg" class="notice-icon"></image>
    <text>This code is secured with government-level encryption and can be verified by authorized personnel only.</text>
  </view>
</view>

