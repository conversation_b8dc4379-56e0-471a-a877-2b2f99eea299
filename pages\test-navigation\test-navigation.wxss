.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* Space for bottom nav */
}

/* Main Content */
.main-content {
  padding: 40rpx 20rpx;
}

.test-text {
  display: block;
  margin: 20rpx 0;
  font-size: 32rpx;
  color: #333;
}

.content-block {
  background-color: white;
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 10rpx;
  font-size: 28rpx;
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  line-height: 1;
}

.nav-text {
  font-size: 22rpx;
  line-height: 1;
}
