App({
  globalData: {
    userInfo: {
      name: '',
      company: '',
      isLoggedIn: false
    },
    fontSize: 'default',
    liteMode: false
  },

  onLaunch: function () {
    // Load saved font size and lite mode on app launch
    const savedFontSize = wx.getStorageSync('fontSize') || 'default';
    const savedLiteMode = wx.getStorageSync('liteMode');
    this.globalData.fontSize = savedFontSize;
    this.globalData.liteMode = savedLiteMode === true || savedLiteMode === 'true' ? true : false;
    this.applyGlobalFontSize(savedFontSize);

    // try {
    //   console.log("About to call wx.authentication...");
      
    //   wx.authentication({
    //     clientID: "bcff81d61e1b469b8eb6f6dd034e2305",
    //     lang:"en-US",
    //     redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q",
    //     responseType: "code",
    //     scope: "eidapi_auth",
    //     source: "Mini_Program",
    //     state: "123456",
    //     success: (res) => {
    //       console.log("=== APP.JS SUCCESS CALLBACK ===");
    //       console.log("Authentication success in app.js:", res);
    //       console.log("AuthCode received:", res.authCode);
    //       console.log("=== END APP.JS SUCCESS ===");
    //     },
    //     fail: (err) => {
    //       console.log("=== APP.JS FAIL CALLBACK ===");
    //       console.error("Authentication failed in app.js:", err);
    //       console.error("Error details:", JSON.stringify(err));
    //       console.log("=== END APP.JS FAIL ===");
    //     }
    //   });
      
    //   console.log("wx.authentication call completed in app.js");
      
    // } catch (error) {
    //   console.error("Exception when calling wx.authentication:", error);
    // }
  },

  applyGlobalFontSize: function (fontSize) {
    // Apply font size class to page element
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (currentPage.setData) {
        currentPage.setData({
          globalFontSize: fontSize
        });
      }
    }
  },

  setGlobalFontSize: function (fontSize) {
    this.globalData.fontSize = fontSize;
    wx.setStorageSync('fontSize', fontSize);

    // Apply to all current pages
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.setData) {
        page.setData({
          globalFontSize: fontSize
        });
      }
    });
  },

  setGlobalLiteMode: function (liteMode) {
    this.globalData.liteMode = liteMode;
    wx.setStorageSync('liteMode', liteMode);

    // Apply to all current pages
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.setData) {
        page.setData({
          liteMode: liteMode
        });
      }
    });
  },

  setGlobalTheme: function (isLoggedIn) {
    this.globalData.isLoggedIn = isLoggedIn;
    
    // Apply to all current pages
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.setData) {
        page.setData({
          isLoggedIn: isLoggedIn
        });
      }
    });
  }
})








