<view class="container font-{{globalFontSize}} {{isLoggedIn ? 'logged-in-theme' : ''}}">
  <view class="header">
    <view class="back-button" bindtap="navigateBack">
      <image src="/images/back.svg" class="back-icon"></image>
    </view>
    <text class="header-title">Topics</text>
  </view>
  
  <view class="topics-grid">
    <view class="topic-item" wx:for="{{topics}}" wx:key="index">
      <image src="{{item.currentIcon || item.icon}}" class="topic-icon"></image>
      <text class="topic-title">{{item.currentTitle || item.title}}</text>
    </view>
  </view>
  
  <!-- Common Tab Bar Component -->
  <tab-bar active="services"></tab-bar>
</view>






