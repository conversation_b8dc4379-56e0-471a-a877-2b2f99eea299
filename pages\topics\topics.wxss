.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  padding: 10rpx;
  margin-right: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.topics-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 40rpx 20rpx;
  background-color: white;
}

.topic-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.topic-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.topic-title {
  font-size: 28rpx;
  text-align: center;
  color: #333;
  max-width: 90%;
}

/* Font size classes */
.font-small .header-title {
  font-size: 32rpx;
}

.font-small .topic-title {
  font-size: 26rpx;
}

.font-default .header-title {
  font-size: 36rpx;
}

.font-default .topic-title {
  font-size: 28rpx;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .topic-title {
  font-size: 32rpx;
}




