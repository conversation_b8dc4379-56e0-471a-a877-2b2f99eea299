Page({
  data: {
    searchQuery: '',
    activeTab: 'theme',
    globalFontSize: 'default',
    liteMode: false,
    isLoggedIn: false,
    categories: [
      {
        title: 'Life Events',
        icon: '/images/services/life-events.svg'
      },
      {
        title: 'Immigration & Identification',
        icon: '/images/services/immigration.svg'
      },
      {
        title: 'Housing, Environment & Land',
        icon: '/images/environment.svg'
      },
      {
        title: 'Social Services & Benefits',
        icon: '/images/services/social-services.svg'
      },
      {
        title: 'Government Info & Services',
        icon: '/images/services/government-info.svg'
      },
      {
        title: 'Health & Medical',
        icon: '/images/services/health.svg'
      },
      {
        title: 'Recreation & Culture',
        icon: '/images/services/recreation.svg'
      },
      {
        title: 'Education & Training',
        icon: '/images/services/education.svg'
      },
      {
        title: 'Work & Employment',
        icon: '/images/services/work.svg'
      },
      {
        title: 'Taxes, Assets & Finances',
        icon: '/images/services/taxes.svg'
      },
      {
        title: 'Trade & Business',
        icon: '/images/services/trade.svg'
      },
      {
        title: 'Transport & Motoring',
        icon: '/images/services/transport.svg'
      },
      {
        title: 'Public Utility',
        icon: '/images/services/public-utility.svg'
      },
      {
        title: 'Communications',
        icon: '/images/services/communications.svg'
      },
      {
        title: 'Cross Boundary',
        icon: '/images/services/cross-boundary.svg'
      },
      {
        title: 'Notification',
        icon: '/images/notification.svg'
      }
    ],
    governmentOrgs: [
      {
        name: 'Hong Kong Computer Emergency Response Team Coordination Centre',
        logo: '/images/orgs/hkcert.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Office for Film, Newspaper and Article Administration',
        logo: '/images/orgs/ofnaa.png',
        icon: '/images/government.svg'
      },
      {
        name: '深圳市政务服务和数据管理局',
        logo: '/images/orgs/shenzhen.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Agriculture, Fisheries and Conservation Department',
        logo: '/images/orgs/afcd.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Auxiliary Medical Service',
        logo: '/images/orgs/ams.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Buildings Department',
        logo: '/images/orgs/bd.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Census and Statistics Department',
        logo: '/images/orgs/csd.png',
        icon: '/images/government.svg'
      },
      {
        name: 'Chief Secretary for Administration',
        logo: '/images/orgs/csa.png',
        icon: '/images/government.svg'
      }
    ],
    commercialCategories: [
      {
        name: 'Credit Reference',
        expanded: true,
        organizations: [
          {
            name: 'TransUnion Credit Information Services Limited',
            logo: '/images/orgs/transunion.png'
          }
        ]
      },
      {
        name: 'Public Utility',
        expanded: false,
        organizations: [
          {
            name: 'CLP Power Hong Kong Limited',
            logo: '/images/orgs/clp.png'
          },
          {
            name: 'Hong Kong Electric Company',
            logo: '/images/orgs/hkelectric.png'
          },
          {
            name: 'Towngas',
            logo: '/images/orgs/towngas.png'
          }
        ]
      },
      {
        name: 'Charity Organisation',
        expanded: false,
        organizations: [
          {
            name: 'Hong Kong Red Cross',
            logo: '/images/orgs/redcross.png'
          },
          {
            name: 'Oxfam Hong Kong',
            logo: '/images/orgs/oxfam.png'
          }
        ]
      },
      {
        name: 'Communications & Technology',
        expanded: false,
        organizations: [
          {
            name: 'Hong Kong Telecommunications',
            logo: '/images/orgs/hkt.png'
          },
          {
            name: 'SmarTone',
            logo: '/images/orgs/smartone.png'
          }
        ]
      },
      {
        name: 'Culture',
        expanded: false,
        organizations: [
          {
            name: 'Hong Kong Arts Centre',
            logo: '/images/orgs/hkac.png'
          },
          {
            name: 'Hong Kong Museum of Art',
            logo: '/images/orgs/hkma.png'
          }
        ]
      },
      {
        name: 'Financial Services',
        expanded: false,
        organizations: [
          {
            name: 'HSBC',
            logo: '/images/orgs/hsbc.png'
          },
          {
            name: 'Bank of China (Hong Kong)',
            logo: '/images/orgs/bochk.png'
          }
        ]
      },
      {
        name: 'Healthcare',
        expanded: false,
        organizations: [
          {
            name: 'Hong Kong Sanatorium & Hospital',
            logo: '/images/orgs/hksh.png'
          },
          {
            name: 'Union Hospital',
            logo: '/images/orgs/union.png'
          }
        ]
      },
      {
        name: 'Information & Communications Technology',
        expanded: false,
        organizations: [
          {
            name: 'Hong Kong Science and Technology Parks Corporation',
            logo: '/images/orgs/hkstp.png'
          },
          {
            name: 'Cyberport',
            logo: '/images/orgs/cyberport.png'
          }
        ]
      }
    ],
    // Touch tracking for swipe
    touchStartX: 0,
    touchEndX: 0,
    globalFontSize: 'default'
  },
  
  onLoad: function() {
    // Load global font size and lite mode
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onShow: function() {
    // Update font size and lite mode when returning to page
    const app = getApp();
    this.setData({
      globalFontSize: app.globalData.fontSize,
      liteMode: app.globalData.liteMode || false,
      isLoggedIn: app.globalData.userInfo && app.globalData.userInfo.isLoggedIn
    });
  },
  
  onSearchInput: function(e) {
    this.setData({
      searchQuery: e.detail.value
    });
  },
  
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  
  handleTouchStart: function(e) {
    this.setData({
      touchStartX: e.touches[0].clientX
    });
  },
  
  handleTouchMove: function(e) {
    this.setData({
      touchEndX: e.touches[0].clientX
    });
  },
  
  handleTouchEnd: function() {
    const { touchStartX, touchEndX } = this.data;
    const minSwipeDistance = 50; // Minimum distance to be considered a swipe
    
    if (touchStartX - touchEndX > minSwipeDistance) {
      // Swiped left - go to next tab
      this.switchToNextTab();
    } else if (touchEndX - touchStartX > minSwipeDistance) {
      // Swiped right - go to previous tab
      this.switchToPrevTab();
    }
  },
  
  switchToNextTab: function() {
    const tabs = ['theme', 'government', 'commercial'];
    const currentIndex = tabs.indexOf(this.data.activeTab);
    
    if (currentIndex < tabs.length - 1) {
      this.setData({
        activeTab: tabs[currentIndex + 1]
      });
    }
  },
  
  switchToPrevTab: function() {
    const tabs = ['theme', 'government', 'commercial'];
    const currentIndex = tabs.indexOf(this.data.activeTab);
    
    if (currentIndex > 0) {
      this.setData({
        activeTab: tabs[currentIndex - 1]
      });
    }
  },
  
  onCategoryTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const category = this.data.categories[index];
    console.log('Category tapped:', category.title);

    // Handle notification category specifically
    if (category.title === 'Notification') {
      wx.navigateTo({
        url: '/pages/notification/write_noti'
      });
      return;
    }

    // Navigate to category detail page or perform other actions for other categories
  },
  
  onOrgTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const org = this.data.governmentOrgs[index];
    console.log('Organization tapped:', org.name);
    // Navigate to organization detail page or perform other actions
  },
  
  navigateBack: function() {
    wx.navigateBack();
  },
  
  toggleCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    const expandedKey = `commercialCategories[${index}].expanded`;
    const currentExpanded = this.data.commercialCategories[index].expanded;
    
    this.setData({
      [expandedKey]: !currentExpanded
    });
  },
  
  onCommercialOrgTap: function(e) {
    const categoryIndex = e.currentTarget.dataset.categoryIndex;
    const orgIndex = e.currentTarget.dataset.orgIndex;
    const org = this.data.commercialCategories[categoryIndex].organizations[orgIndex];
    
    console.log('Selected commercial organization:', org.name);
    // Navigate to organization detail page when implemented
  },
  
  // Bottom navigation handlers
  navigateToHome: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },
  
  navigateToServices: function() {
    // Already on services page
    console.log('Already on services page');
  },
  
  onScanTap: function() {
    // Temporarily disabled due to scanner crashes
    wx.showToast({
      title: 'Scan feature temporarily unavailable',
      icon: 'none'
    });
    
    /*
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('QR Code scan result:', res);
        this.handleQRCodeResult(res.result);
      },
      fail: (err) => {
        console.log('QR scan failed:', err);
        wx.showToast({
          title: 'QR scan cancelled',
          icon: 'none'
        });
      }
    });
    */
  },
  
  navigateToMe: function() {
    // Check if user is logged in first
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLoggedIn) {
      wx.showToast({
        title: 'Please login first',
        icon: 'none'
      });
      return;
    }
    
    // Start re-authentication process
    this.startReAuthentication();
  },

  startReAuthentication: function() {
    console.log("Starting re-authentication process");
    
    // Show loading
    wx.showLoading({
      title: 'Re-authenticating...',
    });

    // Step 1: Get a ticketID from the system
    wx.request({
      url: 'https://dev.dapdapdap.online/testiamsmart/eservice/miniapp/api/auth/request-reauth-ticket',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        source: "Mini_Program"
      },
      success: (res) => {
        console.log("Ticket request success:", res);
        
        if (res.statusCode === 200 && res.data && res.data.ticketID) {
          this.performReAuthentication(res.data.ticketID);
        } else {
          console.error("Failed to get ticketID:", res);
          wx.hideLoading();
          wx.showToast({
            title: 'Failed to get re-auth ticket',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("Failed to request ticketID:", err);
        wx.hideLoading();
        wx.showToast({
          title: 'Network error getting ticket',
          icon: 'none'
        });
      }
    });
  },

  performReAuthentication: function(ticketID) {
    console.log("Performing re-authentication with ticketID:", ticketID);
    
    const reAuthParams = {
      ticketID: ticketID,
      source: "Mini_Program", 
      redirectURI: "tcmpprg4hne0fjw://applet/?appId=mpyhkysqoik9t14q&action=reauth",
      
      success: (res) => {
        console.log("=== RE-AUTH SUCCESS ===");
        console.log("Re-authentication success:", res);
        
        wx.hideLoading();
        
        wx.navigateTo({
          url: '/pages/me/me'
        });
      },
      
      fail: (err) => {
        console.log("=== RE-AUTH FAILED ===");
        console.error("Re-authentication failed:", err);
        
        wx.hideLoading();
        wx.showToast({
          title: 'Re-authentication failed',
          icon: 'none'
        });
      }
    };

    console.log("Calling wx.reAuthentication with params:", reAuthParams);
    
    try {
      wx.reAuthentication(reAuthParams);
    } catch (error) {
      console.error("Exception during re-authentication:", error);
      wx.hideLoading();
      wx.showToast({
        title: 'Re-authentication error',
        icon: 'none'
      });
    }
  },
  
  navigateToSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  }
})



  /*
  handleQRCodeResult: function(result) {
    if (result.includes('corpid') || result.includes('personal-code')) {
      wx.navigateTo({
        url: '/pages/personal-code/personal-code?code=' + encodeURIComponent(result)
      });
    } else {
      wx.showModal({
        title: 'QR Code Scanned',
        content: result,
        showCancel: false
      });
    }
  },
  */




