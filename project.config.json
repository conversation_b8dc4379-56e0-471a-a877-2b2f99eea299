{"projectid": "touristId_TestFlight-Miniapp", "setting": {"urlCheck": false, "es6": true, "postcss": false, "minified": true, "newFeature": true, "autoAudits": false, "nodeModules": true, "uploadWithSourceMap": true, "uglifyFileName": true, "remoteDebugLogEnable": true, "prefetch": false}, "TCMPPLibVersion": "2.2.8", "compileType": "miniprogram", "createTime": 1752467035174, "accessTime": 1752467035174, "packOptions": {"ignore": []}, "debugOptions": {"hidedInDevtools": []}, "TCMPPappid": "mpyhkysqoik9t14q", "projectname": "TestFlight-Miniapp", "scripts": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "plugin": {"current": -1, "list": []}, "gamePlugin": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}