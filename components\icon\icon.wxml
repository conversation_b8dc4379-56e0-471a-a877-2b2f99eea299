<view class="icon-container" style="width: {{size}}rpx; height: {{size}}rpx;">
  <image wx:if="{{type === 'back'}}" src="/images/back.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'search'}}" src="/images/search.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'voice'}}" src="/images/voice.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'arrow'}}" src="/images/arrow-right.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'eye'}}" src="/images/eye.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'edit'}}" src="/images/edit.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'layers'}}" src="/images/layers.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'display'}}" src="/images/display.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'qrcode'}}" src="/images/qrcode.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'storage'}}" src="/images/storage.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'guide'}}" src="/images/guide.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'help'}}" src="/images/help.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'about'}}" src="/images/about.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'terms'}}" src="/images/terms.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'list'}}" src="/images/list.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:elif="{{type === 'settings'}}" src="/images/settings.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
  <image wx:else src="/images/default.svg" style="width: {{size}}rpx; height: {{size}}rpx;"></image>
</view>
