.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.back-button, .help-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .help-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

/* Banner */
.banner {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #006633;
  color: #ffffff;
}

.banner-icon {
  width: 100rpx;
  height: 100rpx;
  margin-right: 30rpx;
}

.banner-text {
  flex: 1;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* Service Section */
.service-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 30rpx;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.service-item:active {
  background-color: #e0e0e0;
}

.service-icon-container {
  width: 100rpx;
  height: 100rpx;
  background-color: #e8f5f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
}

.service-name {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
}

/* Information Section */
.info-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.info-card {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.info-card:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #006633;
  margin-bottom: 15rpx;
}

.info-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}