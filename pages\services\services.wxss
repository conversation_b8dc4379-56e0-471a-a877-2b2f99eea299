.container {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  padding-bottom: 110rpx;
  padding-top: 0;
}

.header {
  padding: 30rpx;
  text-align: center;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  position: relative;
}

/* Orange gradient theme for logged in users */
.logged-in-theme .header {
  background: linear-gradient(135deg, #FE7C22, #FFBF2E) !important;
}

.logged-in-theme .header-title {
  color: white !important;
}

.back-button {
  position: absolute;
  left: 30rpx;
  padding: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* Search Bar */
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
}

.search-input-container {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  margin: 0 20rpx;
  font-size: 28rpx;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  min-height: 90rpx; /* Increased height to accommodate 3 lines */
  display: flex;
  flex-direction: column;
  justify-content: center;
  line-height: 1.3;
}

.tab.active {
  color: #006633;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background-color: #006633;
}

/* Categories Grid */
.categories-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
  background-color: #ffffff;
}

.category-item {
  width: 33.33%; /* Ensure exactly 3 items per row */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 3rpx; /* Further reduced padding */
  box-sizing: border-box;
}

.category-icon {
  width: 60rpx; /* Reduced icon size */
  height: 60rpx;
  margin-bottom: 8rpx; /* Reduced bottom margin */
}

.category-title {
  font-size: 20rpx; /* Smaller font size */
  text-align: center;
  color: #333333;
  line-height: 1.1; /* Tighter line height */
  word-wrap: break-word;
  max-width: 100%;
}

/* Government Organizations List */
.government-list {
  background-color: #ffffff;
}

.government-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.org-logo-container {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 50%;
  overflow: hidden;
}

.org-logo {
  width: 60rpx;
  height: 60rpx;
}

.org-info {
  flex: 1;
}

.org-name {
  font-size: 30rpx;
  color: #333333;
  line-height: 1.4;
}

.org-arrow {
  padding: 0 10rpx;
}

/* Commercial Organizations List */
.commercial-list {
  background-color: #ffffff;
}

.commercial-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.category-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.category-arrow {
  transition: transform 0.3s;
}

.category-arrow.expanded {
  transform: rotate(180deg);
}

.commercial-orgs {
  background-color: #f9f9f9;
}

.commercial-org-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  padding-left: 60rpx;
  border-bottom: 1px solid #f0f0f0;
}

/* Empty State */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

/* Add this to your existing CSS */
.content-area {
  flex: 1;
  width: 100%;
  overflow-x: hidden;
  touch-action: pan-y;
}

/* Font size classes */
.font-small .header-title {
  font-size: 32rpx;
}

.font-small .search-input {
  font-size: 26rpx;
}

.font-small .tab {
  font-size: 26rpx;
}

.font-small .category-title {
  font-size: 18rpx;
}

.font-small .org-name {
  font-size: 28rpx;
}

.font-small .category-name {
  font-size: 28rpx;
}

.font-default .header-title {
  font-size: 36rpx;
}

.font-default .search-input {
  font-size: 28rpx;
}

.font-default .tab {
  font-size: 28rpx;
}

.font-default .category-title {
  font-size: 20rpx;
}

.font-default .org-name {
  font-size: 32rpx;
}

.font-default .category-name {
  font-size: 32rpx;
}

.font-large .header-title {
  font-size: 40rpx;
}

.font-large .search-input {
  font-size: 32rpx;
}

.font-large .tab {
  font-size: 32rpx;
}

.font-large .category-title {
  font-size: 24rpx;
}

.font-large .org-name {
  font-size: 36rpx;
}

.font-large .category-name {
  font-size: 36rpx;
}

/* Lite Mode Styles - Added at the end */
.lite-search-container {
  padding: 20rpx;
  background-color: #ffffff;
}

.lite-search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  border: 2rpx solid #e0e0e0;
}

.lite-search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.lite-search-input {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
}

.lite-mic-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 20rpx;
}

.lite-tab-navigation {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  overflow-x: auto;
  white-space: nowrap;
}

.lite-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 28rpx;
  color: #666666;
  border-bottom: 4rpx solid transparent;
  min-width: 200rpx;
  white-space: normal;
  line-height: 1.2;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lite-tab.active {
  color: #006633;
  border-bottom-color: #006633;
  font-weight: 500;
}

.lite-content-area {
  flex: 1;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.lite-categories-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.lite-category-item {
  background-color: #e8f5e8;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.lite-category-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.3;
}

.lite-government-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.lite-government-item {
  background-color: #e8f5e8;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-bottom: none;
}

.lite-org-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.3;
}

.lite-commercial-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  background-color: transparent;
  border-radius: 0;
}

.lite-commercial-org-item {
  background-color: #e8f5e8;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-bottom: none;
}

.lite-commercial-category {
  display: none;
}

.lite-commercial-orgs {
  display: none;
}

.lite-org-name,
.lite-category-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

/* Font size classes for navigation */
.font-small .nav-text {
  font-size: 20rpx;
}

.font-default .nav-text {
  font-size: 22rpx;
}

.font-large .nav-text {
  font-size: 24rpx;
}

/* Bottom Navigation - copied exactly from index page */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #e0e0e0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  padding-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 10rpx 5rpx;
}

.nav-item.active {
  color: #006633;
}

.nav-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 22rpx;
  line-height: 1;
}

.scan {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rpx;
}

.scan-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: #006633;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -10rpx;
  margin-bottom: -6rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}

/* Font size classes for homepage - copied from index */
.font-small .title {
  font-size: 28rpx;
}

.font-small .subtitle {
  font-size: 28rpx;
}

.font-small .login,
.font-small .temperature,
.font-small .nav-text,
.font-small .news-headline,
.font-small .tab,
.font-small .more,
.font-small .service-title,
.font-small .harbour-title,
.font-small .harbour-location,
.font-small .harbour-destination {
  font-size: 26rpx;
}

.font-small .speed-value {
  font-size: 34rpx;
}

.font-small .speed-unit,
.font-small .fee-item {
  font-size: 20rpx;
}

.font-default .speed-value {
  font-size: 40rpx;
}

.font-default .speed-unit,
.font-default .fee-item {
  font-size: 24rpx;
}


.font-default .title {
  font-size: 32rpx;
}

.font-default .subtitle {
  font-size: 32rpx;
}

.font-default .login,
.font-default .temperature,
.font-default .nav-text,
.font-default .news-headline,
.font-default .tab,
.font-default .more,
.font-default .service-title,
.font-default .harbour-title,
.font-default .harbour-location,
.font-default .harbour-destination {
  font-size: 28rpx;
}

.font-large .title {
  font-size: 36rpx;
}

.font-large .subtitle {
  font-size: 36rpx;
}

.font-large .login,
.font-large .temperature,
.font-large .nav-text,
.font-large .news-headline,
.font-large .tab,
.font-large .more,
.font-large .service-title,
.font-large .harbour-title,
.font-large .harbour-location,
.font-large .harbour-destination {
  font-size: 32rpx;
}

.font-large .speed-value {
  font-size: 46rpx;
}

.font-large .speed-unit,
.font-large .fee-item {
  font-size: 28rpx;
}

.font-small .service-title,
.font-small .topic-title,
.font-small .harbour-title,
.font-small .harbour-location,
.font-small .harbour-destination {
  font-size: 26rpx;
}

.font-default .service-title,
.font-default .topic-title,
.font-default .harbour-title,
.font-default .harbour-location,
.font-default .harbour-destination {
  font-size: 28rpx;
}

.font-large .service-title,
.font-large .topic-title,
.font-large .harbour-title,
.font-large .harbour-location,
.font-large .harbour-destination {
  font-size: 32rpx;
}

/* Lite Mode Styles */
.lite-todo-section {
  margin: 20rpx;
  background-color: white;
  border: 2rpx solid #ff4444;
  border-radius: 20rpx;
  padding: 30rpx;
}

.tab-navigation .tab,
.lite-tab-navigation .lite-tab {
  font-size: 28rpx !important;
}

.font-small .tab-navigation .tab,
.font-small .lite-tab-navigation .lite-tab {
  font-size: 26rpx !important;
}

.font-default .tab-navigation .tab,
.font-default .lite-tab-navigation .lite-tab {
  font-size: 28rpx !important;
}

.font-large .tab-navigation .tab,
.font-large .lite-tab-navigation .lite-tab {
  font-size: 32rpx !important;
}

.tab text,
.lite-tab text {
  font-size: 28rpx !important;
}

.font-small .tab text,
.font-small .lite-tab text {
  font-size: 26rpx !important;
}

.font-default .tab text,
.font-default .lite-tab text {
  font-size: 28rpx !important;
}

.font-large .tab text,
.font-large .lite-tab text {
  font-size: 32rpx !important;
}

.tab .category-title,
.lite-tab .category-title {
  font-size: 28rpx !important;
}

.font-small .tab .category-title,
.font-small .lite-tab .category-title {
  font-size: 26rpx !important;
}

.font-default .tab .category-title,
.font-default .lite-tab .category-title {
  font-size: 28rpx !important;
}

.font-large .tab .category-title,
.font-large .lite-tab .category-title {
  font-size: 32rpx !important;
}
