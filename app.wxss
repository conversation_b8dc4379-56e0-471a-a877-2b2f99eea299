/**app.wxss**/
page {
  height: 100%;
  font-size: 32rpx;
  line-height: 1.6;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding-top: env(safe-area-inset-top);
}

/* Global font size classes */
.font-small {
  font-size: 28rpx;
}

.font-default {
  font-size: 32rpx;
}

.font-large {
  font-size: 36rpx;
}

/* Apply font size to common text elements */
.font-small text,
.font-small .nav-text,
.font-small .service-title,
.font-small .setting-label {
  font-size: 28rpx;
}

.font-large text,
.font-large .nav-text,
.font-large .service-title,
.font-large .setting-label {
  font-size: 36rpx;
}

/* Global container with proper bottom spacing */
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 0;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom) + 20rpx); /* Updated to match tab-bar height */
}

/* For pages that need extra spacing */
.page-container {
  position: relative;
  padding-top: 0;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom) + 20rpx); /* Updated to match tab-bar height */
}

/* Alternative: Add status bar height */
.status-bar-height {
  height: var(--status-bar-height);
}

/* Lite Mode Styles */
.lite-mode {
  background-color: #f0f0f0 !important;
}

.lite-mode .settings-section {
  background-color: #ffffff !important;
}

/* Hide specific menu items in lite mode - Updated to match JS logic */
.lite-mode .settings-list .list-item:nth-child(4), /* User Guide (index 3) */
.lite-mode .settings-list .list-item:nth-child(9), /* Privacy Policy (index 7) */
.lite-mode .settings-list .list-item:nth-child(10) /* Feedback (index 8) */
{
  display: none !important;
}

/* Simplify colors in lite mode */
.lite-mode .section-title,
.lite-mode .item-title {
  color: #333333 !important;
}

/* Global theme classes for login state */
.logged-in-theme {
  --primary-color: #ff6b35;
  --primary-light: #ff8c5a;
  --primary-dark: #e55a2b;
  --accent-color: #ffa366;
}

.logged-in-theme .banner {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
}

.logged-in-theme .tab-bar {
  background-color: var(--primary-color) !important;
}

.logged-in-theme .header {
  background-color: var(--primary-color) !important;
}

.logged-in-theme .settings-section,
.logged-in-theme .service-category {
  border-left: 4px solid var(--primary-color) !important;
}

.logged-in-theme .service-item:hover,
.logged-in-theme .list-item:hover {
  background-color: rgba(255, 107, 53, 0.1) !important;
}

.logged-in-theme .switch-checked {
  background-color: var(--primary-color) !important;
}

















