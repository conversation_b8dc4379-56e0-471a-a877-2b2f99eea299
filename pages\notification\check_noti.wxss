/* Basic styles for check_noti page */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.back-button {
  margin-right: 10px;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.content {
  flex: 1;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 100rpx;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.primary-button {
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
}

.primary-button:disabled {
  background-color: #ccc;
}

.status-section {
  margin-top: 20px;
}

.status-title {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.status-text {
  width: 100%;
  height: 300px;  /* Doubled from 150px */
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #f9f9f9;
}

.error-section {
  margin-top: 20px;
}

.error-title {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: red;
}

.error-text {
  color: red;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  visibility: hidden;
}

.loading-overlay.visible {
  visibility: visible;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  color: white;
  font-size: 16px;
}