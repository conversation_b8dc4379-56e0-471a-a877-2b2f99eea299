.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button, .settings-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .settings-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

/* Introduction Slides */
.intro-slides {
  height: 500rpx;
  margin-bottom: 20rpx;
}

.slide-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  height: 100%;
}

.slide-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}

.slide-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #006633;
  margin-bottom: 20rpx;
  text-align: center;
}

.slide-description {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

/* Chat Container */
.chat-container {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
}

.message {
  margin-bottom: 30rpx;
  max-width: 80%;
  display: flex;
  flex-direction: column;
}

.user-message {
  align-self: flex-end;
  margin-left: auto;
}

.assistant-message {
  align-self: flex-start;
  margin-right: auto;
}

.message-content {
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message .message-content {
  background-color: #006633;
  color: #ffffff;
  border-top-right-radius: 4rpx;
}

.assistant-message .message-content {
  background-color: #ffffff;
  color: #333333;
  border-top-left-radius: 4rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.message-time {
  font-size: 22rpx;
  color: #999999;
  margin-top: 10rpx;
  align-self: flex-end;
}

/* Suggestions */
.suggestions-container {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
}

.suggestions-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.suggestion-item {
  background-color: #f0f0f0;
  padding: 15rpx 25rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #333333;
}

/* Input Area */
.input-container {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  position: sticky;
  bottom: 0;
}

.input-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 40rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.input-box input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.voice-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-icon {
  width: 40rpx;
  height: 40rpx;
}

.send-button {
  background-color: #cccccc;
  color: #ffffff;
  padding: 15rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.send-button.active {
  background-color: #006633;
}